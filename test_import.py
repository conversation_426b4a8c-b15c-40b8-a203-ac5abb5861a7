#!/usr/bin/env python3
import sys
sys.path.append('.')

print("开始测试导入...")

try:
    import sapien
    print("✅ SAPIEN导入成功")
    
    import envs
    print("✅ envs模块导入成功")
    
    from envs.blocks_stack_three import blocks_stack_three
    print("✅ blocks_stack_three导入成功")
    
    env = blocks_stack_three()
    print("✅ 环境实例创建成功")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
