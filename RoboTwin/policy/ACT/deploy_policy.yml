# Basic experiment configuration
task_name: null
head_camera_type: D435
policy_name: ACT
setting: null # modify this
seed: null
instruction_type: unseen

# ACT-specific arguments
kl_weight: 10.0
chunk_size: 50 
hidden_dim: 512
dim_feedforward: 3200
temporal_agg: false  
device: cuda:0

# DETR parser args
ckpt_dir: null
policy_class: ACT
num_epochs: 2000

# Model training params
position_embedding: sine
lr_backbone: 0.00001
weight_decay: 0.0001
lr: 0.00001
masks: false
dilation: false
backbone: resnet18
nheads: 8
enc_layers: 4
dec_layers: 7
pre_norm: false
dropout: 0.1
camera_names:
  - cam_high
  - cam_right_wrist
  - cam_left_wrist
temporal_agg: true