
import numpy as np
import torch
import hydra
import dill
import sys, os
current_file_path = os.path.abspath(__file__)
parent_dir = os.path.dirname(current_file_path)
sys.path.append(parent_dir)
from diffusion_policy.workspace.robotworkspace import RobotWorkspace
from diffusion_policy.env_runner.dp_runner import D<PERSON>unner

class DP:
    def __init__(self, ckpt_file:str):
        self.policy = self.get_policy(ckpt_file, None, 'cuda:0')
        self.runner = DPRunner(output_dir=None)

    def update_obs(self, observation):
        self.runner.update_obs(observation)
    
    def get_action(self, observation=None):
        action = self.runner.get_action(self.policy, observation)
        return action

    def get_last_obs(self):
        return self.runner.obs[-1]

    def get_policy(self, checkpoint, output_dir, device):
        # load checkpoint
        payload = torch.load(open(checkpoint, 'rb'), pickle_module=dill)
        cfg = payload['cfg']
        cls = hydra.utils.get_class(cfg._target_)
        workspace = cls(cfg, output_dir=output_dir)
        workspace: RobotWorkspace
        workspace.load_payload(payload, exclude_keys=None, include_keys=None)
        
        # get policy from workspace
        policy = workspace.model
        if cfg.training.use_ema:
            policy = workspace.ema_model
        
        device = torch.device(device)
        policy.to(device)
        policy.eval()

        return policy

def encode_obs(observation):
    head_cam = np.moveaxis(observation['observation']['head_camera']['rgb'], -1, 0) / 255
    # front_cam = np.moveaxis(observation['observation']['front_camera']['rgb'], -1, 0) / 255
    left_cam = np.moveaxis(observation['observation']['left_camera']['rgb'], -1, 0) / 255
    right_cam = np.moveaxis(observation['observation']['right_camera']['rgb'], -1, 0) / 255
    obs = dict(
        head_cam = head_cam,
        # front_cam = front_cam,
        left_cam = left_cam,
        right_cam = right_cam
    )
    obs['agent_pos'] = observation['joint_action']['vector']
    return obs

def get_model(usr_args):
    ckpt_file = f"./policy/DP/checkpoints/{usr_args['task_name']}-{usr_args['setting']}-{usr_args['expert_data_num']}-{usr_args['seed']}/{usr_args['checkpoint_num']}.ckpt"
    return DP(ckpt_file)

def eval(TASK_ENV, model, observation):
    '''
    TASK_ENV: Task Environment Class, you can use this class to interact with the environment
    model: The model from 'get_model()' function
    observation: The observation about the environment 
    '''
    obs = encode_obs(observation)
    # ======== Get Action ========
    actions = model.get_action(obs)

    for action in actions:
        TASK_ENV.take_action(action)
        observation = TASK_ENV.get_obs()
        obs = encode_obs(observation)
        model.update_obs(obs)

def reset_model(model):
    model.runner.reset_obs()