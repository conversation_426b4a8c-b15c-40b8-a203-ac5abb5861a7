{"fractal20220817_data": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "taco_play": {"image_keys": ["rgb_static", "rgb_gripper", "rgb_static", "rgb_static"], "image_mask": [1, 1, 0, 0]}, "jaco_play": {"image_keys": ["image", "image_wrist", "image_wrist", "image_wrist"], "image_mask": [1, 1, 0, 0]}, "berkeley_cable_routing": {"image_keys": ["image", "wrist45_image", "wrist225_image", "top_image"], "image_mask": [1, 1, 0, 1]}, "nyu_door_opening_surprising_effectiveness": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "viola": {"image_keys": ["agentview_rgb", "eye_in_hand_rgb", "eye_in_hand_rgb", "eye_in_hand_rgb"], "image_mask": [1, 1, 0, 0]}, "berkeley_autolab_ur5": {"image_keys": ["image", "hand_image", "hand_image", "hand_image"], "image_mask": [1, 1, 0, 0]}, "toto": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "kuka": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "language_table": {"image_keys": ["rgb", "rgb", "rgb", "rgb"], "image_mask": [1, 0, 0, 0]}, "columbia_cairlab_pusht_real": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "stanford_kuka_multimodal_dataset_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "nyu_rot_dataset_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "stanford_hydra_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "austin_buds_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "nyu_franka_play_dataset_converted_externally_to_rlds": {"image_keys": ["image", "image_additional_view", "image_additional_view", "image_additional_view"], "image_mask": [1, 0, 0, 1]}, "maniskill_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "furniture_bench_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "ucsd_kitchen_dataset_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "ucsd_pick_and_place_dataset_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "austin_sailor_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "austin_sirius_dataset_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "bc_z": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "utokyo_pr2_opening_fridge_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "utokyo_pr2_tabletop_manipulation_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "utokyo_xarm_pick_and_place_converted_externally_to_rlds": {"image_keys": ["image", "hand_image", "hand_image", "image2"], "image_mask": [1, 1, 0, 1]}, "utokyo_xarm_bimanual_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "berkeley_mvp_converted_externally_to_rlds": {"image_keys": ["hand_image", "hand_image", "hand_image", "hand_image"], "image_mask": [0, 1, 0, 0]}, "berkeley_rpt_converted_externally_to_rlds": {"image_keys": ["hand_image", "hand_image", "hand_image", "hand_image"], "image_mask": [0, 1, 0, 0]}, "kaist_nonprehensile_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "stanford_mask_vit_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "tokyo_u_lsmo_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "dlr_sara_pour_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "dlr_sara_grid_clamp_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "dlr_edan_shared_control_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "asu_table_top_converted_externally_to_rlds": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "stanford_robocook_converted_externally_to_rlds": {"image_keys": ["image_2", "image_1", "image_3", "image_4"], "image_mask": [1, 0, 0, 1]}, "eth_agent_affordances": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "imperialcollege_sawyer_wrist_cam": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [0, 1, 0, 0]}, "iamlab_cmu_pickup_insert_converted_externally_to_rlds": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "uiuc_d3field": {"image_keys": ["image_1", "image_2", "image_3", "image_4"], "image_mask": [1, 0, 0, 1]}, "utaustin_mutex": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "berkeley_fanuc_manipulation": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "cmu_play_fusion": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "cmu_stretch": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "berkeley_gnm_recon": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "berkeley_gnm_cory_hall": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "berkeley_gnm_sac_son": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "robo_net": {"image_keys": ["image", "image1", "image2", "image2"], "image_mask": [1, 0, 0, 1]}, "roboturk_real_towercreation": {"image_keys": ["top_rgb_frame", "front_rgb_frame", "front_rgb_frame", "front_rgb_frame"], "image_mask": [1, 0, 0, 1]}, "roboturk_real_laundrylayout": {"image_keys": ["top_rgb_frame", "front_rgb_frame", "front_rgb_frame", "front_rgb_frame"], "image_mask": [1, 0, 0, 1]}, "roboturk_real_objectsearch": {"image_keys": ["top_rgb_frame", "front_rgb_frame", "front_rgb_frame", "front_rgb_frame"], "image_mask": [1, 0, 0, 1]}, "aloha_mobile": {"image_keys": ["cam_high", "cam_right_wrist", "cam_left_wrist", "cam_right_wrist"], "image_mask": [1, 1, 1, 0]}, "aloha_static": {"image_keys": ["cam_high", "cam_right_wrist", "cam_left_wrist", "cam_low"], "image_mask": [1, 1, 1, 1]}, "roboset": {"image_keys": ["rgb_top", "rgb_right", "rgb_left", "rgb_right"], "image_mask": [1, 1, 1, 0]}, "droid": {"image_keys": ["exterior_image_1_left", "wrist_image_left", "wrist_image_left", "exterior_image_2_left"], "image_mask": [1, 1, 0, 1]}, "fmb": {"image_keys": ["image_side_1", "image_wrist_1", "image_wrist_1", "image_side_2"], "image_mask": [1, 1, 0, 1]}, "dobbe": {"image_keys": ["wrist_image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [0, 1, 0, 0]}, "qut_dexterous_manpulation": {"image_keys": ["image", "wrist_image", "wrist_image", "wrist_image"], "image_mask": [1, 1, 0, 0]}, "agilex": {"image_keys": ["cam_high", "cam_right_wrist", "cam_left_wrist", "cam_right_wrist"], "image_mask": [1, 1, 1, 0]}, "rh20t": {"image_keys": ["image", "image", "image", "image"], "image_mask": [1, 0, 0, 0]}, "calvin": {"image_keys": ["rgb_static", "rgb_gripper", "rgb_gripper", "rgb_gripper"], "image_mask": [1, 1, 0, 0]}, "bridgev2": {"image_keys": ["images0", "images0", "images0", "images0"], "image_mask": [1, 0, 0, 0]}}