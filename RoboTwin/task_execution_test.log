/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/_vulkan_tricks.py:37: UserWarning: Failed to find Vulkan ICD file. This is probably due to an incorrect or partial installation of the NVIDIA driver. SAPIEN will attempt to provide an ICD file anyway but it may not work.
  warn(
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/test_task_execution.py", line 30, in test_task_execution
    module = __import__(module_name, fromlist=[task_name])
ModuleNotFoundError: No module named 'envs._camera_config'
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/test_task_execution.py", line 30, in test_task_execution
    module = __import__(module_name, fromlist=[task_name])
ModuleNotFoundError: No module named 'envs._embodiment_config'
🚀 开始任务执行测试...
发现 8 个任务配置:
  - _camera_config
  - _embodiment_config
  - blocks_ranking_rgb
  - blocks_stack_three
  - dual_shoes_place
  - place_object_scale
  - place_phone_stand
  - put_bottles_dustbin

🧪 测试 _camera_config 任务执行...
  ✅ 配置文件读取成功
[Planner.py]: Curobo not installed! No module named 'curobo'
  ❌ _camera_config 任务测试失败: No module named 'envs._camera_config'

🧪 测试 _embodiment_config 任务执行...
  ✅ 配置文件读取成功
  ❌ _embodiment_config 任务测试失败: No module named 'envs._embodiment_config'

🧪 测试 blocks_ranking_rgb 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 blocks_ranking_rgb 任务测试通过!

🧪 测试 blocks_stack_three 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 blocks_stack_three 任务测试通过!

🧪 测试 dual_shoes_place 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 dual_shoes_place 任务测试通过!

🧪 测试 place_object_scale 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 place_object_scale 任务测试通过!

🧪 测试 place_phone_stand 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 place_phone_stand 任务测试通过!

🧪 测试 put_bottles_dustbin 任务执行...
  ✅ 配置文件读取成功
  ✅ 环境类导入成功
  ✅ 环境实例创建成功
  ⚠️  setup_demo 执行失败: failed to find a rendering device
  🎉 put_bottles_dustbin 任务测试通过!

==================================================
📊 任务执行测试结果总结:
==================================================
  _camera_config            ❌ 失败
  _embodiment_config        ❌ 失败
  blocks_ranking_rgb        ✅ 通过
  blocks_stack_three        ✅ 通过
  dual_shoes_place          ✅ 通过
  place_object_scale        ✅ 通过
  place_phone_stand         ✅ 通过
  put_bottles_dustbin       ✅ 通过

总计: 6/8 个任务测试通过
⚠️  部分任务测试失败，请检查上述错误信息
