#!/usr/bin/env python3
"""
简化的任务运行脚本
绕过渲染问题，直接运行任务
"""

import sys
import os
import yaml
import traceback
import time
sys.path.append('.')

def run_task_simple(task_name, episode_num=1):
    """简化的任务运行函数"""
    print(f"\n🚀 开始运行 {task_name} 任务...")
    print(f"📊 计划运行 {episode_num} 个episode")
    
    try:
        # 读取任务配置
        task_config_path = f'task_config/{task_name}.yml'
        with open(task_config_path, 'r', encoding='utf-8') as f:
            config = yaml.load(f.read(), Loader=yaml.FullLoader)
        print(f"✅ 配置文件读取成功")
        
        # 导入环境
        module_name = f'envs.{task_name}'
        module = __import__(module_name, fromlist=[task_name])
        env_class = getattr(module, task_name)
        print(f"✅ 环境类导入成功")
        
        # 创建保存目录
        save_path = f'./simple_test_data/{task_name}'
        os.makedirs(save_path, exist_ok=True)
        print(f"✅ 创建保存目录: {save_path}")
        
        success_count = 0
        fail_count = 0
        
        for episode in range(episode_num):
            print(f"\n📝 运行 Episode {episode + 1}/{episode_num}...")
            
            try:
                # 创建环境实例
                env = env_class()
                
                # 简化的参数设置
                args = {
                    'task_name': task_name,
                    'render_freq': 0,  # 无渲染模式
                    'need_plan': True,
                    'is_save': False,  # 暂时不保存数据
                    'save_path': save_path,
                    'embodiment_name': 'test',
                    'setting': 'test',
                    'dual_arm_embodied': config.get('dual_arm', True),
                    'augmentation': {
                        'messy_table': False,
                        'random_background': False,
                        'random_light': False,
                        'random_table_height': 0,
                        'random_head_camera_dis': 0,
                    },
                    'camera': {
                        'head_camera_type': 'D435',
                        'wrist_camera_type': 'D435',
                        'collect_head_camera': False,
                        'collect_wrist_camera': False,
                    },
                    'data_type': {
                        'rgb': False,
                        'depth': False,
                        'observer': False,
                        'endpose': False,
                        'qpos': True,
                    },
                }
                
                # 设置演示
                env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
                print(f"  ✅ 环境设置完成")
                
                # 运行一次
                start_time = time.time()
                result = env.play_once()
                end_time = time.time()
                
                # 检查成功
                if hasattr(env, 'check_success'):
                    success = env.check_success()
                    if success:
                        success_count += 1
                        print(f"  🎉 Episode {episode + 1} 成功! (耗时: {end_time - start_time:.2f}s)")
                    else:
                        fail_count += 1
                        print(f"  ❌ Episode {episode + 1} 失败 (耗时: {end_time - start_time:.2f}s)")
                else:
                    success_count += 1
                    print(f"  ✅ Episode {episode + 1} 完成 (耗时: {end_time - start_time:.2f}s)")
                
                # 清理
                if hasattr(env, 'close'):
                    env.close()
                    
            except Exception as e:
                fail_count += 1
                print(f"  ❌ Episode {episode + 1} 异常: {e}")
                traceback.print_exc()
                
                # 尝试清理
                try:
                    if 'env' in locals() and hasattr(env, 'close'):
                        env.close()
                except:
                    pass
        
        # 总结结果
        print(f"\n📊 {task_name} 任务运行完成:")
        print(f"  ✅ 成功: {success_count}/{episode_num}")
        print(f"  ❌ 失败: {fail_count}/{episode_num}")
        print(f"  📈 成功率: {success_count/episode_num*100:.1f}%")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ {task_name} 任务运行失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 简化任务运行测试")
    
    # 可用任务列表
    tasks = [
        'blocks_stack_three',
        'dual_shoes_place', 
        'place_object_scale',
        'place_phone_stand',
        'put_bottles_dustbin',
        'blocks_ranking_rgb'
    ]
    
    results = {}
    
    for task in tasks:
        results[task] = run_task_simple(task, episode_num=2)
        time.sleep(2)  # 短暂休息
    
    # 最终总结
    print("\n" + "="*60)
    print("🏆 所有任务运行结果总结:")
    print("="*60)
    
    for task, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {task:<25} {status}")
    
    successful_tasks = sum(results.values())
    total_tasks = len(results)
    print(f"\n总计: {successful_tasks}/{total_tasks} 个任务运行成功")

if __name__ == "__main__":
    main()
