# Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES.  All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto.  Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.

# This file list the additional files that should be include in the package distribution.
#
# References:
# * https://packaging.python.org/guides/using-manifest-in/
# * https://setuptools.readthedocs.io/en/latest/userguide/datafiles.html
# * https://stackoverflow.com/questions/6028000/how-to-read-a-static-file-from-inside-a-python-package

# graft <path to files or directories to add to the project>
graft src/curobo/content
global-include py.typed
graft src/curobo/curobolib/*.so