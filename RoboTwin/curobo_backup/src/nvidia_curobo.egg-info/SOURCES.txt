.clangd
.gitattributes
.gitignore
CHANGELOG.md
CODEOWNERS
LICENSE
LICENSE_ASSETS
MANIFEST.in
README.md
pyproject.toml
setup.cfg
setup.py
.github/ISSUE_TEMPLATE/standard-template.md
benchmark/README.md
benchmark/curobo_benchmark.py
benchmark/curobo_nvblox_benchmark.py
benchmark/curobo_nvblox_profile.py
benchmark/curobo_profile.py
benchmark/curobo_python_profile.py
benchmark/curobo_voxel_benchmark.py
benchmark/curobo_voxel_profile.py
benchmark/generate_nvblox_images.py
benchmark/ik_benchmark.py
benchmark/kinematics_benchmark.py
benchmark/metrics.py
benchmark/robometrics_benchmark.py
docker/.dockerignore
docker/README.md
docker/aarch64.dockerfile
docker/base.dockerfile
docker/build_dev_docker.sh
docker/build_docker.sh
docker/isaac_sim.dockerfile
docker/start_dev_docker.sh
docker/start_docker.sh
docker/start_docker_aarch64.sh
docker/start_docker_isaac_sim.sh
docker/start_docker_isaac_sim_headless.sh
docker/start_docker_x86.sh
docker/start_user_docker.sh
docker/user.dockerfile
docker/user_isaac_sim.dockerfile
docker/x86.dockerfile
examples/collision_check_example.py
examples/ik_example.py
examples/kinematics_example.py
examples/mesh_dataset.py
examples/motion_gen_api_example.py
examples/motion_gen_example.py
examples/motion_gen_profile.py
examples/mpc_example.py
examples/nvblox_example.py
examples/pose_sequence_example.py
examples/robot_image_segmentation_example.py
examples/torch_layers_example.py
examples/trajopt_example.py
examples/usd_example.py
examples/world_representation_example.py
examples/isaac_sim/batch_collision_checker.py
examples/isaac_sim/batch_motion_gen_reacher.py
examples/isaac_sim/collision_checker.py
examples/isaac_sim/constrained_reacher.py
examples/isaac_sim/helper.py
examples/isaac_sim/ik_reachability.py
examples/isaac_sim/load_all_robots.py
examples/isaac_sim/motion_gen_reacher.py
examples/isaac_sim/motion_gen_reacher_nvblox.py
examples/isaac_sim/mpc_example.py
examples/isaac_sim/mpc_nvblox_example.py
examples/isaac_sim/multi_arm_reacher.py
examples/isaac_sim/realsense_collision.py
examples/isaac_sim/realsense_mpc.py
examples/isaac_sim/realsense_reacher.py
examples/isaac_sim/realsense_viewer.py
examples/isaac_sim/simple_stacking.py
examples/isaac_sim/util/convert_urdf_to_usd.py
examples/isaac_sim/util/dowload_assets.py
images/robot_demo.gif
images/rrt_compare.gif
src/curobo/__init__.py
src/curobo/py.typed
src/curobo/util_file.py
src/curobo/content/assets/robot/franka_description/franka_panda.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_mobile.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_mobile_base.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_mobile_x.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_mobile_xy.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_mobile_xy_tz.urdf
src/curobo/content/assets/robot/franka_description/franka_panda_no_gripper.urdf
src/curobo/content/assets/robot/franka_description/franka_test.urdf
src/curobo/content/assets/robot/franka_description/moving_gripper.urdf
src/curobo/content/assets/robot/franka_description/panda.urdf
src/curobo/content/assets/robot/franka_description/panda_grasp_gripper.urdf
src/curobo/content/assets/robot/franka_description/panda_gripper.urdf
src/curobo/content/assets/robot/franka_description/meshes/collision/finger.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/finger.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/hand.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/hand.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/hand_flipped.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/hand_gripper.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/hand_gripper.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/hand_gripper_flipped.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link0.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link0.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link1.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link1.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link2.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link2.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link3.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link3.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link4.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link4.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link5.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link5.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link6.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link6.stl
src/curobo/content/assets/robot/franka_description/meshes/collision/link7.obj
src/curobo/content/assets/robot/franka_description/meshes/collision/link7.stl
src/curobo/content/assets/robot/franka_description/meshes/visual/finger.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/finger.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/finger.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/graspnet_panda_mesh.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/hand.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/hand.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/hand.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/hand_ee_link.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/hand_gripper.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/hand_gripper.stl
src/curobo/content/assets/robot/franka_description/meshes/visual/link0.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link0.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link0.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link0.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link1.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link1.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link1.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link1.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link2.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link2.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link2.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link2.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link3.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link3.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link3.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link3.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link4.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link4.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link4.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link4.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link5.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link5.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link5.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link5.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link6.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link6.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link6.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link6.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link7.dae
src/curobo/content/assets/robot/franka_description/meshes/visual/link7.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/link7.obj
src/curobo/content/assets/robot/franka_description/meshes/visual/link7.obj.mtl
src/curobo/content/assets/robot/franka_description/meshes/visual/train_gripper.obj
src/curobo/content/assets/robot/iiwa_allegro_description/allegro.urdf
src/curobo/content/assets/robot/iiwa_allegro_description/iiwa.urdf
src/curobo/content/assets/robot/iiwa_allegro_description/iiwa_allegro.urdf
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/base_link.STL
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/base_link.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_base.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_base.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_medial.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_medial.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_proximal.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/primary_proximal.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_base.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_base.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_medial.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_medial.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_proximal.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/allegro/thumb_proximal.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/biotac/biotac_sensor.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/biotac/biotac_sensor.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/biotac/biotac_sensor_thumb.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/biotac/biotac_sensor_thumb.stl
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_0.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_1.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_2.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_3.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_4.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_5.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_6.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/collision/link_7.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_0.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_1.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_2.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_3.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_4.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_5.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_6.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/iiwa7/visual/link_7.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/mounts/allegro_mount.obj
src/curobo/content/assets/robot/iiwa_allegro_description/meshes/mounts/allegro_mount.stl
src/curobo/content/assets/robot/jaco/jaco_7n.urdf
src/curobo/content/assets/robot/jaco/jaco_7s.urdf
src/curobo/content/assets/robot/jaco/meshes/arm.dae
src/curobo/content/assets/robot/jaco/meshes/arm_half_1.dae
src/curobo/content/assets/robot/jaco/meshes/arm_half_2.dae
src/curobo/content/assets/robot/jaco/meshes/arm_mico.dae
src/curobo/content/assets/robot/jaco/meshes/base.dae
src/curobo/content/assets/robot/jaco/meshes/finger_distal.dae
src/curobo/content/assets/robot/jaco/meshes/finger_proximal.dae
src/curobo/content/assets/robot/jaco/meshes/forearm.dae
src/curobo/content/assets/robot/jaco/meshes/forearm_mico.dae
src/curobo/content/assets/robot/jaco/meshes/hand_2finger.dae
src/curobo/content/assets/robot/jaco/meshes/hand_3finger.dae
src/curobo/content/assets/robot/jaco/meshes/ring_big.dae
src/curobo/content/assets/robot/jaco/meshes/ring_small.dae
src/curobo/content/assets/robot/jaco/meshes/shoulder.dae
src/curobo/content/assets/robot/jaco/meshes/wrist.dae
src/curobo/content/assets/robot/jaco/meshes/wrist_spherical_1.dae
src/curobo/content/assets/robot/jaco/meshes/wrist_spherical_2.dae
src/curobo/content/assets/robot/kinova/README.md
src/curobo/content/assets/robot/kinova/kinova_gen3_7dof.urdf
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/base_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/bracelet_no_vision_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/bracelet_with_vision_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/end_effector_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/forearm_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/half_arm_1_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/half_arm_2_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/shoulder_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/spherical_wrist_1_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/arms/gen3/7dof/meshes/spherical_wrist_2_link.STL
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/LICENSE
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/README.md
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_140_inner_finger.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_140_inner_knuckle.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_140_outer_finger.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_140_outer_knuckle.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_base_link.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/collision/robotiq_arg2f_coupling.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_140_inner_finger.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_140_inner_knuckle.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_140_outer_finger.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_140_outer_knuckle.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_base_link.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_140/meshes/visual/robotiq_arg2f_coupling.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/LICENSE
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/README.md
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_base_link.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_finger.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_knuckle.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_finger.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_knuckle.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_base_link.stl
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_base_link.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_finger.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_knuckle.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_finger.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_knuckle.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_pad.dae
src/curobo/content/assets/robot/kinova/kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_gripper_coupling.stl
src/curobo/content/assets/robot/simple/simple_mimic_robot.urdf
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/collision/tm12-arm1_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/collision/tm12-arm2_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/collision/tm12-base_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/collision/tmr_750w_01_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm1.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm1.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm1.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm2.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm2.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-arm2.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-base.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-base.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tm12-base.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tmr_750w_01.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tmr_750w_01.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm12/visual/tmr_750w_01.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tm5-900_arm1_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tm5-900_arm2_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tm5-base_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tmr_100w_01_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tmr_100w_02_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tmr_400w_01_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tmr_ee_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/collision/tmr_iox_c.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm1.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm1.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm1.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm2.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm2.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-900_arm2.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-base.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-base.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tm5-base.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_01.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_01.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_01.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_02.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_02.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_100w_02.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_400w_01.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_400w_01.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_400w_01.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_ee.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_ee.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_ee.stl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_iox.mtl
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_iox.obj
src/curobo/content/assets/robot/techman/tm_description/meshes/tm5-900/visual/tmr_iox.stl
src/curobo/content/assets/robot/techman/tm_description/urdf/tm12-nominal.urdf
src/curobo/content/assets/robot/techman/tm_description/urdf/tm12x-nominal.urdf
src/curobo/content/assets/robot/ur_description/dual_ur10e.urdf
src/curobo/content/assets/robot/ur_description/quad_ur10e.urdf
src/curobo/content/assets/robot/ur_description/tri_ur10e.urdf
src/curobo/content/assets/robot/ur_description/ur10e.urdf
src/curobo/content/assets/robot/ur_description/ur5e.urdf
src/curobo/content/assets/robot/ur_description/ur5e_robotiq_2f_140.urdf
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/base.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/forearm.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/shoulder.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/upperarm.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/wrist1.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/wrist2.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/collision/wrist3.stl
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/base.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/forearm.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/shoulder.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/upperarm.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/wrist1.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/wrist2.dae
src/curobo/content/assets/robot/ur_description/meshes/ur10e/visual/wrist3.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/base.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/forearm.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/shoulder.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/upperarm.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/wrist1.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/wrist2.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/collision/wrist3.stl
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/base.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/forearm.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/shoulder.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/upperarm.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/wrist1.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/wrist2.dae
src/curobo/content/assets/robot/ur_description/meshes/ur5e/visual/wrist3.dae
src/curobo/content/assets/scene/nvblox/srl_ur10_bins.mtl
src/curobo/content/assets/scene/nvblox/srl_ur10_bins.nvblx
src/curobo/content/assets/scene/nvblox/srl_ur10_bins.obj
src/curobo/content/assets/scene/nvblox/srl_ur10_bins_color.png
src/curobo/content/configs/robot/dual_ur10e.yml
src/curobo/content/configs/robot/franka.yml
src/curobo/content/configs/robot/franka_mobile.yml
src/curobo/content/configs/robot/iiwa.yml
src/curobo/content/configs/robot/iiwa_allegro.yml
src/curobo/content/configs/robot/jaco7.yml
src/curobo/content/configs/robot/kinova_gen3.yml
src/curobo/content/configs/robot/quad_ur10e.yml
src/curobo/content/configs/robot/simple_mimic_robot.yml
src/curobo/content/configs/robot/template.yml
src/curobo/content/configs/robot/tm12.yml
src/curobo/content/configs/robot/tri_ur10e.yml
src/curobo/content/configs/robot/ur10e.xrdf
src/curobo/content/configs/robot/ur10e.yml
src/curobo/content/configs/robot/ur5e.yml
src/curobo/content/configs/robot/ur5e_robotiq_2f_140.yml
src/curobo/content/configs/robot/isaac_sim_description/jaco_7s.yaml
src/curobo/content/configs/robot/isaac_sim_description/tm12.yaml
src/curobo/content/configs/robot/isaac_sim_description/ur5e.yaml
src/curobo/content/configs/robot/spheres/dual_ur10e.yml
src/curobo/content/configs/robot/spheres/franka.yml
src/curobo/content/configs/robot/spheres/franka_collision_mesh.yml
src/curobo/content/configs/robot/spheres/franka_mesh.yml
src/curobo/content/configs/robot/spheres/franka_mesh_inside.yml
src/curobo/content/configs/robot/spheres/franka_mesh_mb.yml
src/curobo/content/configs/robot/spheres/franka_real_robot.yml
src/curobo/content/configs/robot/spheres/iiwa_allegro.yml
src/curobo/content/configs/robot/spheres/kinova_gen3.yml
src/curobo/content/configs/robot/spheres/quad_ur10e.yml
src/curobo/content/configs/robot/spheres/single.yml
src/curobo/content/configs/robot/spheres/ur10e.yml
src/curobo/content/configs/task/base_cfg.yml
src/curobo/content/configs/task/finetune_trajopt.yml
src/curobo/content/configs/task/finetune_trajopt_slow.yml
src/curobo/content/configs/task/gradient_ik.yml
src/curobo/content/configs/task/gradient_ik_autotune.yml
src/curobo/content/configs/task/gradient_mpc.yml
src/curobo/content/configs/task/gradient_trajopt.yml
src/curobo/content/configs/task/graph.yml
src/curobo/content/configs/task/particle_ik.yml
src/curobo/content/configs/task/particle_mpc.yml
src/curobo/content/configs/task/particle_trajopt.yml
src/curobo/content/configs/world/collision_base.yml
src/curobo/content/configs/world/collision_cage.yml
src/curobo/content/configs/world/collision_cubby.yml
src/curobo/content/configs/world/collision_floor_plan.yml
src/curobo/content/configs/world/collision_handover.yml
src/curobo/content/configs/world/collision_mesh_scene.yml
src/curobo/content/configs/world/collision_nvblox.yml
src/curobo/content/configs/world/collision_nvblox_online.yml
src/curobo/content/configs/world/collision_nvblox_ur10.yml
src/curobo/content/configs/world/collision_pillar_wall.yml
src/curobo/content/configs/world/collision_primitives_3d.yml
src/curobo/content/configs/world/collision_table.yml
src/curobo/content/configs/world/collision_test.yml
src/curobo/content/configs/world/collision_thin_walls.yml
src/curobo/content/configs/world/collision_wall.yml
src/curobo/cuda_robot_model/__init__.py
src/curobo/cuda_robot_model/cuda_robot_generator.py
src/curobo/cuda_robot_model/cuda_robot_model.py
src/curobo/cuda_robot_model/kinematics_parser.py
src/curobo/cuda_robot_model/types.py
src/curobo/cuda_robot_model/urdf_kinematics_parser.py
src/curobo/cuda_robot_model/usd_kinematics_parser.py
src/curobo/cuda_robot_model/util.py
src/curobo/curobolib/__init__.py
src/curobo/curobolib/geom.py
src/curobo/curobolib/kinematics.py
src/curobo/curobolib/ls.py
src/curobo/curobolib/opt.py
src/curobo/curobolib/tensor_step.py
src/curobo/curobolib/util_file.py
src/curobo/curobolib/cpp/check_cuda.h
src/curobo/curobolib/cpp/cuda_precisions.h
src/curobo/curobolib/cpp/geom_cuda.cpp
src/curobo/curobolib/cpp/helper_math.h
src/curobo/curobolib/cpp/kinematics_fused_cuda.cpp
src/curobo/curobolib/cpp/kinematics_fused_kernel.cu
src/curobo/curobolib/cpp/lbfgs_step_cuda.cpp
src/curobo/curobolib/cpp/lbfgs_step_kernel.cu
src/curobo/curobolib/cpp/line_search_cuda.cpp
src/curobo/curobolib/cpp/line_search_kernel.cu
src/curobo/curobolib/cpp/pose_distance_kernel.cu
src/curobo/curobolib/cpp/self_collision_kernel.cu
src/curobo/curobolib/cpp/sphere_obb_kernel.cu
src/curobo/curobolib/cpp/tensor_step_cuda.cpp
src/curobo/curobolib/cpp/tensor_step_kernel.cu
src/curobo/curobolib/cpp/update_best_kernel.cu
src/curobo/geom/__init__.py
src/curobo/geom/cv.py
src/curobo/geom/sphere_fit.py
src/curobo/geom/transform.py
src/curobo/geom/types.py
src/curobo/geom/sdf/__init__.py
src/curobo/geom/sdf/sdf_grid.py
src/curobo/geom/sdf/utils.py
src/curobo/geom/sdf/warp_primitives.py
src/curobo/geom/sdf/warp_sdf_fns.py
src/curobo/geom/sdf/warp_sdf_fns_deprecated.py
src/curobo/geom/sdf/world.py
src/curobo/geom/sdf/world_blox.py
src/curobo/geom/sdf/world_mesh.py
src/curobo/geom/sdf/world_voxel.py
src/curobo/graph/__init__.py
src/curobo/graph/graph_base.py
src/curobo/graph/graph_nx.py
src/curobo/graph/prm.py
src/curobo/opt/__init__.py
src/curobo/opt/opt_base.py
src/curobo/opt/newton/__init__.py
src/curobo/opt/newton/lbfgs.py
src/curobo/opt/newton/newton_base.py
src/curobo/opt/particle/__init__.py
src/curobo/opt/particle/parallel_es.py
src/curobo/opt/particle/parallel_mppi.py
src/curobo/opt/particle/particle_opt_base.py
src/curobo/opt/particle/particle_opt_utils.py
src/curobo/rollout/__init__.py
src/curobo/rollout/arm_base.py
src/curobo/rollout/arm_reacher.py
src/curobo/rollout/rollout_base.py
src/curobo/rollout/cost/__init__.py
src/curobo/rollout/cost/bound_cost.py
src/curobo/rollout/cost/cost_base.py
src/curobo/rollout/cost/dist_cost.py
src/curobo/rollout/cost/manipulability_cost.py
src/curobo/rollout/cost/pose_cost.py
src/curobo/rollout/cost/primitive_collision_cost.py
src/curobo/rollout/cost/projected_dist_cost.py
src/curobo/rollout/cost/self_collision_cost.py
src/curobo/rollout/cost/stop_cost.py
src/curobo/rollout/cost/straight_line_cost.py
src/curobo/rollout/cost/zero_cost.py
src/curobo/rollout/dynamics_model/__init__.py
src/curobo/rollout/dynamics_model/integration_utils.py
src/curobo/rollout/dynamics_model/kinematic_model.py
src/curobo/rollout/dynamics_model/model_base.py
src/curobo/rollout/dynamics_model/tensor_step.py
src/curobo/types/__init__.py
src/curobo/types/base.py
src/curobo/types/camera.py
src/curobo/types/enum.py
src/curobo/types/file_path.py
src/curobo/types/math.py
src/curobo/types/robot.py
src/curobo/types/state.py
src/curobo/types/tensor.py
src/curobo/util/__init__.py
src/curobo/util/error_metrics.py
src/curobo/util/helpers.py
src/curobo/util/logger.py
src/curobo/util/metrics.py
src/curobo/util/sample_lib.py
src/curobo/util/state_filter.py
src/curobo/util/tensor_util.py
src/curobo/util/torch_utils.py
src/curobo/util/trajectory.py
src/curobo/util/usd_helper.py
src/curobo/util/warp.py
src/curobo/util/warp_interpolation.py
src/curobo/util/xrdf_utils.py
src/curobo/wrap/__init__.py
src/curobo/wrap/wrap_base.py
src/curobo/wrap/wrap_mpc.py
src/curobo/wrap/model/__init__.py
src/curobo/wrap/model/curobo_robot_world.py
src/curobo/wrap/model/robot_segmenter.py
src/curobo/wrap/model/robot_world.py
src/curobo/wrap/reacher/__init__.py
src/curobo/wrap/reacher/evaluator.py
src/curobo/wrap/reacher/ik_solver.py
src/curobo/wrap/reacher/motion_gen.py
src/curobo/wrap/reacher/mpc.py
src/curobo/wrap/reacher/trajopt.py
src/curobo/wrap/reacher/types.py
src/nvidia_curobo.egg-info/PKG-INFO
src/nvidia_curobo.egg-info/SOURCES.txt
src/nvidia_curobo.egg-info/dependency_links.txt
src/nvidia_curobo.egg-info/requires.txt
src/nvidia_curobo.egg-info/top_level.txt
tests/__init__.py
tests/conftest.py
tests/cost_test.py
tests/cuda_graph_test.py
tests/cuda_robot_generator_test.py
tests/curobo_robot_world_model_test.py
tests/curobo_version_test.py
tests/geom_test.py
tests/geom_types_test.py
tests/goal_test.py
tests/ik_config_test.py
tests/ik_module_test.py
tests/ik_test.py
tests/interpolation_test.py
tests/kinematics_parser_test.py
tests/kinematics_test.py
tests/mimic_joint_test.py
tests/motion_gen_api_test.py
tests/motion_gen_constrained_test.py
tests/motion_gen_cuda_graph_test.py
tests/motion_gen_eval_test.py
tests/motion_gen_goalset_test.py
tests/motion_gen_js_test.py
tests/motion_gen_module_test.py
tests/motion_gen_speed_test.py
tests/motion_gen_test.py
tests/mpc_test.py
tests/multi_pose_test.py
tests/nvblox_test.py
tests/pose_reaching_test.py
tests/pose_test.py
tests/robot_assets_test.py
tests/robot_config_test.py
tests/robot_segmentation_test.py
tests/robot_world_model_test.py
tests/self_collision_test.py
tests/trajopt_config_test.py
tests/trajopt_test.py
tests/usd_export_test.py
tests/voxel_collision_test.py
tests/voxelization_test.py
tests/warp_mesh_test.py
tests/world_config_test.py
tests/xrdf_test.py