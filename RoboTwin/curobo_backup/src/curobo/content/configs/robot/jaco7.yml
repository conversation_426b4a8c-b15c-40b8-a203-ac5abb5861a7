##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

robot_cfg:

  kinematics:

    urdf_path: "robot/jaco/jaco_7s.urdf"
    asset_root_path: "robot/jaco"
    usd_path: "robot/jaco/jaco_7s.usda"
    isaac_usd_path: "/Isaac/Robots/Kinova/Jaco2/J2N7S300/j2n7s300_instanceable.usd"
    usd_robot_root: "/jaco"
    #usd_robot_root: "/robot"
    usd_flip_joints: {
        j2s7s300_joint_1: "Z",
        j2s7s300_joint_2: "Z",
        j2s7s300_joint_3: "Z",
        j2s7s300_joint_4: "Z",
        j2s7s300_joint_5: "Z",
        j2s7s300_joint_6: "Z",
        j2s7s300_joint_7 : "Z",
        j2s7s300_joint_finger_1: "Z",
        j2s7s300_joint_finger_2: "Z",
        j2s7s300_joint_finger_3: "Z",
        j2s7s300_joint_finger_tip_1: "Z",
        j2s7s300_joint_finger_tip_2: "Z",
        j2s7s300_joint_finger_tip_3: "Z",
    }
    base_link: "root"
    ee_link: "j2s7s300_end_effector"
    link_names: null

    collision_link_names: [
      "j2s7s300_link_base", "j2s7s300_link_1", "j2s7s300_link_2",
      "j2s7s300_link_3", "j2s7s300_link_4", "j2s7s300_link_5", "j2s7s300_link_6", "j2s7s300_link_7",
      "j2s7s300_link_finger_tip_1", "j2s7s300_link_finger_tip_2",
      "j2s7s300_link_finger_tip_3",
      "j2s7s300_link_finger_3",
      "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_1",
      ]

    collision_spheres:
      j2s7s300_link_base:
        - "center": [0.0, -0.003, 0.103]
          "radius": 0.048
        - "center": [0.0, -0.0, 0.162]
          "radius": 0.047
      j2s7s300_link_1:
        - "center": [-0.004, -0.002, -0.056]
          "radius": 0.05
        - "center": [0.0, 0.001, -0.119]
          "radius": 0.05
      j2s7s300_link_2:
        - "center": [0.0, -0.162, -0.001]
          "radius": 0.049
        - "center": [-0.0, -0.0, 0.0]
          "radius": 0.05
        - "center": [0.0, -0.108, -0.001]
          "radius": 0.049
        - "center": [0.0, -0.054, -0.0]
          "radius": 0.05
      j2s7s300_link_3:
        - "center": [-0.004, -0.0, -0.202]
          "radius": 0.05
        - "center": [0.0, 0.0, 0.0]
          "radius": 0.05
        - "center": [-0.003, -0.0, -0.161]
          "radius": 0.05
        - "center": [-0.002, -0.0, -0.121]
          "radius": 0.05
        - "center": [-0.001, -0.0, -0.081]
          "radius": 0.05
        - "center": [-0.001, -0.0, -0.04]
          "radius": 0.05
      j2s7s300_link_4:
        - "center": [0.002, 0.21, -0.013]
          "radius": 0.04
        - "center": [0.0, 0.0, 0.0]
          "radius": 0.05
        - "center": [0.001, 0.172, -0.01]
          "radius": 0.042
        - "center": [0.001, 0.132, -0.008]
          "radius": 0.044
        - "center": [0.001, 0.09, -0.005]
          "radius": 0.046
        - "center": [0.0, 0.046, -0.003]
          "radius": 0.048
      j2s7s300_link_5:
        - "center": [-0.001, 0.013, -0.106]
          "radius": 0.04
        - "center": [-0.003, 0.003, -0.044]
          "radius": 0.04
        - "center": [-0.002, 0.008, -0.075]
          "radius": 0.04
      j2s7s300_link_6:
        - "center": [-0.001, 0.094, 0.0]
          "radius": 0.04
        - "center": [0.0, -0.0, -0.017]
          "radius": 0.04
        - "center": [-0.001, 0.047, -0.008]
          "radius": 0.04
      j2s7s300_link_7:
        - "center": [-0.01, -0.017, -0.069]
          "radius": 0.045
        - "center": [0.001, 0.023, -0.076]
          "radius": 0.038
        - "center": [0.016, -0.022, -0.071]
          "radius": 0.038
        - "center": [-0.0, 0.0, -0.031]
          "radius": 0.04
      j2s7s300_link_finger_tip_1:
        - "center": [0.009, -0.004, -0.0]
          "radius": 0.017
        - "center": [0.03, -0.007, -0.002]
          "radius": 0.015
      j2s7s300_link_finger_tip_2:
        - "center": [0.009, -0.004, -0.0]
          "radius": 0.017
        - "center": [0.032, -0.002, -0.001]
          "radius": 0.015
      j2s7s300_link_finger_2:
        - "center": [0.011, -0.01, -0.0]
          "radius": 0.021
        - "center": [0.032, -0.01, 0.001]
          "radius": 0.02
      j2s7s300_link_finger_3:
        - "center": [0.011, -0.01, -0.0]
          "radius": 0.021
        - "center": [0.032, -0.01, 0.001]
          "radius": 0.02
      j2s7s300_link_finger_tip_3:
        - "center": [0.032, -0.004, 0.001]
          "radius": 0.015
        - "center": [0.015, -0.006, -0.002]
          "radius": 0.015
      j2s7s300_link_finger_1:
        - "center": [0.011, -0.01, -0.0]
          "radius": 0.021
        - "center": [0.032, -0.01, 0.001]
          "radius": 0.02

    collision_sphere_buffer: 0.005
    self_collision_ignore: {
      "j2s7s300_link_base":["j2s7s300_link_1"],
      "j2s7s300_link_1":["j2s7s300_link_2"],
      "j2s7s300_link_2":["j2s7s300_link_3"],
      "j2s7s300_link_3":["j2s7s300_link_4"],
      "j2s7s300_link_4":["j2s7s300_link_5"],
      "j2s7s300_link_5":["j2s7s300_link_6",
      "j2s7s300_link_finger_tip_1",
      "j2s7s300_link_finger_tip_2",
      "j2s7s300_link_finger_tip_3",
      "j2s7s300_link_finger_1",
      "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_3"],
      "j2s7s300_link_6":["j2s7s300_link_7",
      "j2s7s300_link_finger_tip_1",
      "j2s7s300_link_finger_tip_2",
      "j2s7s300_link_finger_tip_3",
      "j2s7s300_link_finger_1",
      "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_3"],
      "j2s7s300_link_7":[
      "j2s7s300_link_finger_tip_1",
      "j2s7s300_link_finger_tip_2",
      "j2s7s300_link_finger_tip_3",
      "j2s7s300_link_finger_1",
      "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_3",
      ],

      "j2s7s300_link_finger_3":
      ["j2s7s300_link_finger_tip_3","j2s7s300_link_finger_2",
       "j2s7s300_link_finger_1", "j2s7s300_link_finger_tip_1", "j2s7s300_link_finger_tip_2"],

      "j2s7s300_link_finger_2":["j2s7s300_link_finger_tip_2", "j2s7s300_link_finger_1",
      "j2s7s300_link_finger_3", "j2s7s300_link_finger_tip_3", "j2s7s300_link_finger_tip_1"],

      "j2s7s300_link_finger_1":["j2s7s300_link_finger_tip_1", "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_3", "j2s7s300_link_finger_tip_3", "j2s7s300_link_finger_tip_2"],

      "j2s7s300_link_finger_tip_1":["j2s7s300_link_finger_tip_2", "j2s7s300_link_finger_tip_3"],

      "j2s7s300_link_finger_tip_2":["j2s7s300_link_finger_tip_3"],

      } # Dict[str, List[str]]
    self_collision_buffer: {
      #"j2s7s300_link_base": 0.02,
      #"j2s7s300_link_1": 0.01,

    } # Dict[str, float]

    mesh_link_names: [
      "j2s7s300_link_base", "j2s7s300_link_1", "j2s7s300_link_2",
      "j2s7s300_link_3", "j2s7s300_link_4", "j2s7s300_link_5", "j2s7s300_link_6", "j2s7s300_link_7",
      "j2s7s300_link_finger_tip_1", "j2s7s300_link_finger_tip_2",
      "j2s7s300_link_finger_tip_3",
      "j2s7s300_link_finger_3",
      "j2s7s300_link_finger_2",
      "j2s7s300_link_finger_1",
    ] # List[str]
    lock_joints: {"j2s7s300_joint_finger_1": 0,
    "j2s7s300_joint_finger_2": 0,
    "j2s7s300_joint_finger_3": 0,
    "j2s7s300_joint_finger_tip_1": 0,
    "j2s7s300_joint_finger_tip_2": 0,
    "j2s7s300_joint_finger_tip_3": 0,

    }

    cspace:
      joint_names:
        - j2s7s300_joint_1
        - j2s7s300_joint_2
        - j2s7s300_joint_3
        - j2s7s300_joint_4
        - j2s7s300_joint_5
        - j2s7s300_joint_6
        - j2s7s300_joint_7
        - j2s7s300_joint_finger_1
        - j2s7s300_joint_finger_2
        - j2s7s300_joint_finger_3
        - j2s7s300_joint_finger_tip_1
        - j2s7s300_joint_finger_tip_2
        - j2s7s300_joint_finger_tip_3
      retract_config: [0.0,4.0,0.0,5,0.0,3.0,0.0, 0,0,0,0,0,0]
      null_space_weight: [1,1,1,1,1,1,1, 1,1,1,1,1,1] # List[str]
      cspace_distance_weight: [1,1,1,1,1,1,1,1,1,1,1,1,1] # List[str]

      max_acceleration: 15.0
      max_jerk: 500.0