##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

robot_cfg:
  kinematics:
    use_usd_kinematics: False
    isaac_usd_path: "/<PERSON>/Robots/Franka/franka.usd"
    usd_path: "robot/franka_description/franka_panda_meters.usda"
    usd_robot_root: "/panda"
    usd_flip_joints: ["panda_joint1","panda_joint2","panda_joint3","panda_joint4", "panda_joint5",
      "panda_joint6","panda_joint7","panda_finger_joint1", "panda_finger_joint2"]
    usd_flip_joints: {
    "panda_joint1": "Z",
    "panda_joint2": "Z",
    "panda_joint3": "Z",
    "panda_joint4": "Z",
     "panda_joint5": "Z",
    "panda_joint6": "Z",
    "panda_joint7": "Z",
    "panda_finger_joint1": "Y",
     "panda_finger_joint2":  "Y",
    }
    usd_flip_joint_limits: ["panda_finger_joint2"]
    urdf_path: "robot/franka_description/franka_panda_mobile_xy_tz.urdf"
    asset_root_path: "robot/franka_description"
    base_link: "base_link"
    ee_link: "panda_hand"
    # link_names: null
    collision_link_names:
      [
        "panda_link0",
        "panda_link1",
        "panda_link2",
        "panda_link3",
        "panda_link4",
        "panda_link5",
        "panda_link6",
        "panda_link7",
        "panda_hand",
        "panda_leftfinger",
        "panda_rightfinger",
        "attached_object",
      ]
    collision_spheres: "spheres/franka_mesh.yml"
    collision_sphere_buffer: 0.002 #0.02
    extra_collision_spheres: {"attached_object": 4}
    use_global_cumul: True
    self_collision_ignore:
      {
        "panda_link0": ["panda_link1", "panda_link2"],
        "panda_link1": ["panda_link2", "panda_link3", "panda_link4"],
        "panda_link2": ["panda_link3", "panda_link4"],
        "panda_link3": ["panda_link4", "panda_link6"],
        "panda_link4":
          ["panda_link5", "panda_link6", "panda_link7", "panda_link8"],
        "panda_link5": ["panda_link6", "panda_link7", "panda_hand","panda_leftfinger", "panda_rightfinger"],
        "panda_link6": ["panda_link7", "panda_hand", "attached_object", "panda_leftfinger", "panda_rightfinger"],
        "panda_link7": ["panda_hand", "attached_object", "panda_leftfinger", "panda_rightfinger"],
        "panda_hand": ["panda_leftfinger", "panda_rightfinger","attached_object"],
        "panda_leftfinger": ["panda_rightfinger", "attached_object"],
        "panda_rightfinger": ["attached_object"],
        
      }
    
    self_collision_buffer:
      {
        "panda_link0": 0.1, 
        "panda_link1": 0.05, 
        "panda_link2": 0.0,
        "panda_link3": 0.0,
        "panda_link4": 0.0,
        "panda_link5": 0.0,
        "panda_link6": 0.0,
        "panda_link7": 0.0,
        "panda_hand": 0.02,
        "panda_leftfinger": 0.01,
        "panda_rightfinger": 0.01,
        "attached_object": 0.0,
      }

    mesh_link_names:
      [
        "panda_link0",
        "panda_link1",
        "panda_link2",
        "panda_link3",
        "panda_link4",
        "panda_link5",
        "panda_link6",
        "panda_link7",
        "panda_hand",
        "panda_leftfinger",
        "panda_rightfinger",
      ]
    lock_joints: {"panda_finger_joint1": 0.04, "panda_finger_joint2": 0.04}
    extra_links: {"attached_object":{"parent_link_name": "panda_hand" , 
    "link_name": "attached_object", "fixed_transform": [0,0,0,1,0,0,0], "joint_type":"FIXED",
    "joint_name": "attach_joint" }}
    cspace:
      joint_names: [
        "base_x", "base_y", "base_z",
        "panda_joint1","panda_joint2","panda_joint3","panda_joint4", "panda_joint5",
      "panda_joint6","panda_joint7","panda_finger_joint1", "panda_finger_joint2"]
      retract_config: [0,0,0,0, -1.3, 0.0, -2.5, 0.0, 1.0, 0., 0.04, 0.04]
      null_space_weight: [1,1,1,1,1,1,1,1,1,1,1,1]
      cspace_distance_weight: [1,1,1,1,1,1,1,1,1,1,1,1]
      max_acceleration: 15.0 #15.0
      max_jerk: 500.0