##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

robot_cfg:
  kinematics:
    use_usd_kinematics: False
    usd_path: "robot/techman/tm12.usd"
    usd_robot_root: "/tm12"
    usd_flip_joints:
      {
        "shoulder_1_joint": "Z",
        "shoulder_2_joint": "Z",
        "elbow_joint": "Z",
        "wrist_1_joint": "Z",
        "wrist_2_joint": "Z",
        "wrist_3_joint": "Z",
      }
    urdf_path: "robot/techman/tm_description/urdf/tm12-nominal.urdf"
    asset_root_path: "robot/techman/tm_description"

    base_link: "base"
    ee_link: "tool0"
    link_names: null

    collision_link_names: [
        "link_1",
        "link_2",
        "link_3",
        "link_4",
        "link_5",
        "link_6",
      ] # List[str]

    collision_spheres:
      link_1:
        - "center": [-0.0, -0.0, 0.018]
          "radius": 0.1
        - "center": [-0.017, -0.18, 0.02]
          "radius": 0.1
      link_2:
        - "center": [0.116, 0.006, -0.182]
          "radius": 0.075
        - "center": [0.638, -0.004, -0.192]
          "radius": 0.08
        - "center": [0.19, 0.004, -0.183]
          "radius": 0.075
        - "center": [0.265, 0.003, -0.184]
          "radius": 0.075
        - "center": [0.34, 0.001, -0.186]
          "radius": 0.075
        - "center": [0.414, 0.0, -0.187]
          "radius": 0.075
        - "center": [0.489, -0.001, -0.189]
          "radius": 0.075
        - "center": [0.563, -0.003, -0.19]
          "radius": 0.075
      link_3:
        - "center": [0.012, 0.004, -0.076]
          "radius": 0.08
        - "center": [0.55, -0.001, -0.046]
          "radius": 0.07
        - "center": [0.088, 0.003, -0.061]
          "radius": 0.06
        - "center": [0.165, 0.002, -0.056]
          "radius": 0.06
        - "center": [0.242, 0.001, -0.052]
          "radius": 0.06
        - "center": [0.319, 0.001, -0.047]
          "radius": 0.06
        - "center": [0.396, -0.0, -0.043]
          "radius": 0.06
        - "center": [0.473, -0.001, -0.038]
          "radius": 0.06
      link_4:
        - "center": [0.0, 0.0, 0.0]
          "radius": 0.07

      link_5:
        - "center": [0.0, 0.0, 0.0]
          "radius": 0.06
      link_6:
        - "center": [0.003, -0.002, -0.028]
          "radius": 0.06
        - "center": [-0.001, 0.075, 0.009]
          "radius": 0.05
        - "center": [-0.0, 0.078, -0.028]
          "radius": 0.05
        - "center": [-0.031, 0.128, 0.008]
          "radius": 0.03
        - "center": [-0.006, 0.146, 0.0]
          "radius": 0.03
        - "center": [0.025, 0.125, 0.007]
          "radius": 0.03
        - "center": [-0.005, 0.128, 0.003]
          "radius": 0.03
    collision_sphere_buffer: 0.0

    self_collision_ignore: {
        "link_1": ["link_2"],
        "link_2": ["link_3"],
        "link_3": ["link_4", "link_5"],
        "link_4": ["link_5", "link_6"],
        "link_5": ["link_6", "link_4"],
      } 
    self_collision_buffer: {
        link_1: -0.01,
        link_2: -0.02,
        link_3: -0.01,
        link_4: -0.02,
        link_6: -0.02,
        link_5: -0.02,
      } # Dict[str, float]

    mesh_link_names: ["link_0", "link_1", "link_2", "link_3", "link_4", "link_5", "link_6"] # List[str]
    lock_joints: null
    add_object_link: False

    cspace:
      joint_names: [
          "shoulder_1_joint",
          "shoulder_2_joint",
          "elbow_joint",
          "wrist_1_joint",
          "wrist_2_joint",
          "wrist_3_joint",
        ] # List[str]
      retract_config: [0.0, -0.5, 1.9, -0.2, 1.25, 0.0] # List[float]
      null_space_weight: [1,1,1,1,1,1] # List[str]
      cspace_distance_weight: [1,1,1,1,1,1] # List[str]

      max_acceleration: 13.5
      max_jerk: 2000.0