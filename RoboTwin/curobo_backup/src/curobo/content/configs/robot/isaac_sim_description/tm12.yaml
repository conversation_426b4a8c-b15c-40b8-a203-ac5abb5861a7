##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

# The robot description defines the generalized coordinates and how to map those
# to the underlying URDF dofs.

api_version: 1.0

# Defines the generalized coordinates. Each generalized coordinate is assumed
# to have an entry in the URDF.
# <PERSON><PERSON> will only use these joints to control the robot position.
cspace:
    - shoulder_1_joint
    - shoulder_2_joint
    - elbow_joint
    - wrist_1_joint
    - wrist_2_joint
    - wrist_3_joint
default_q: [
    -0.1,-0.8,1.5,-0.2,0.2,0.2
]

# Most dimensions of the cspace have a direct corresponding element
# in the URDF. This list of rules defines how unspecified coordinates
# should be extracted or how values in the URDF should be overwritten.

cspace_to_urdf_rules:

# <PERSON><PERSON> uses collision spheres to define the robot geometry in order to avoid
# collisions with external obstacles.  If no spheres are specified, <PERSON><PERSON> will
# not be able to avoid obstacles.

collision_spheres:
  - link_1:
    - "center": [-0.0, -0.0, 0.018]
      "radius": 0.12
    - "center": [0.0, 0.0, -0.13]
      "radius": 0.09
    - "center": [-0.017, -0.18, 0.02]
      "radius": 0.12
  - link_2:
    - "center": [0.116, 0.006, -0.182]
      "radius": 0.075
    - "center": [0.638, -0.004, -0.192]
      "radius": 0.08
    - "center": [0.19, 0.004, -0.183]
      "radius": 0.075
    - "center": [0.265, 0.003, -0.184]
      "radius": 0.075
    - "center": [0.34, 0.001, -0.186]
      "radius": 0.075
    - "center": [0.414, 0.0, -0.187]
      "radius": 0.075
    - "center": [0.489, -0.001, -0.189]
      "radius": 0.075
    - "center": [0.563, -0.003, -0.19]
      "radius": 0.075
  - link_3:
    - "center": [0.012, 0.004, -0.076]
      "radius": 0.08
    - "center": [0.55, -0.001, -0.046]
      "radius": 0.07
    - "center": [0.088, 0.003, -0.061]
      "radius": 0.06
    - "center": [0.165, 0.002, -0.056]
      "radius": 0.06
    - "center": [0.242, 0.001, -0.052]
      "radius": 0.06
    - "center": [0.319, 0.001, -0.047]
      "radius": 0.06
    - "center": [0.396, -0.0, -0.043]
      "radius": 0.06
    - "center": [0.473, -0.001, -0.038]
      "radius": 0.06
  - link_4:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.07
  - link_6:
    - "center": [0.003, -0.002, -0.028]
      "radius": 0.06
    - "center": [-0.001, 0.075, 0.009]
      "radius": 0.05
    - "center": [-0.0, 0.078, -0.028]
      "radius": 0.05
    - "center": [-0.031, 0.128, 0.008]
      "radius": 0.03
    - "center": [-0.006, 0.146, 0.0]
      "radius": 0.03
    - "center": [0.025, 0.125, 0.007]
      "radius": 0.03
    - "center": [-0.005, 0.128, 0.003]
      "radius": 0.03
  - link_5:
    - "center": [0.0, 0.0, 0.0]
      "radius": 0.08
