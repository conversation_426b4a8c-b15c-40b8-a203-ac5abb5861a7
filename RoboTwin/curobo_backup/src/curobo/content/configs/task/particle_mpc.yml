##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

model:
  horizon: 30
  state_filter_cfg:
    filter_coeff:
      position: 0.1
      velocity: 0.1
      acceleration: 0.0
    enable: True
  dt_traj_params:
    base_dt: 0.01
    base_ratio: 0.5
    max_dt: 0.04
  vel_scale: 1.0
  control_space: 'ACCELERATION'
  teleport_mode: False
  state_finite_difference_mode: "CENTRAL"


cost:
  pose_cfg:
    vec_weight: [1.0,1.0, 1.0, 1.0, 1.0, 1.0]
    run_vec_weight: [1.0,1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [60,300.0,20,20] #[150.0, 2000.0, 30, 40]
    vec_convergence: [0.0, 0.00] # orientation, position
    terminal: True
    run_weight: 1.0
    use_metric: True

  cspace_cfg:
    weight: 1000.0
    terminal: True
    run_weight: 1.0

  bound_cfg:
    weight: [5000.0, 5000.0,5000.0,000.0]
    activation_distance: [0.1,0.1,0.1,0.1] # for position, velocity, acceleration and jerk
    smooth_weight: [0.0, 50.0, 0.0,0.0] # [vel, acc, jerk, alpha_vel, eta_position, eta_vel, eta_acc]
    run_weight_velocity: 0.0
    run_weight_acceleration: 1.0
    run_weight_jerk: 1.0
    null_space_weight: [10.0]

  primitive_collision_cfg:
    weight: 100000.0
    use_sweep: True
    sweep_steps: 4
    classify: False
    use_sweep_kernel: True
    use_speed_metric: False
    speed_dt: 0.1 # used only for speed metric
    activation_distance: 0.025

  self_collision_cfg:
    weight: 50000.0
    classify: False


  stop_cfg:
    weight: 100.0 #50.0
    max_nlimit: 0.25 #0.2



mppi:
  init_cov          : 0.05 #.5 #.5
  gamma             : 0.98
  n_iters           : 5
  cold_start_n_iters: 5
  step_size_mean    : 0.9
  step_size_cov     : 0.01
  beta              : 0.1
  alpha             : 1
  num_particles     : 400 #10000
  update_cov        : True
  cov_type          : "DIAG_A" #
  kappa             : 0.0001
  null_act_frac     : 0.05
  sample_mode       : 'BEST'
  base_action       : 'REPEAT'
  squash_fn         : 'CLAMP'
  n_problems        : 1
  use_cuda_graph    : True
  seed              : 0
  store_debug       : False
  random_mean       : True
  sample_per_problem: False
  sync_cuda_time    : True
  use_coo_sparse    : True
  sample_params:
    fixed_samples: True
    sample_ratio: {'halton':0.3, 'halton-knot':0.7, 'random':0.0, 'random-knot':0.0}
    seed: 0
    filter_coeffs: [0.3, 0.3, 0.4]
    n_knots: 5
  debug_info:
    visual_traj       : 'ee_pos_seq'
