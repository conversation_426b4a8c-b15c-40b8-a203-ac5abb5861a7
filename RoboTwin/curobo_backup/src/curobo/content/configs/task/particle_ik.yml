##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

model:
  horizon: 1
  state_filter_cfg:
    filter_coeff:
      position: 1.0
      velocity: 1.0
      acceleration: 0.0
    enable: False
  dt_traj_params:
    base_dt: 0.02
    base_ratio: 1.0
    max_dt: 0.25
  vel_scale: 1.0
  control_space: 'POSITION'
  teleport_mode: True
cost:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [30, 50, 10, 10] #[20.0, 100.0]
    vec_convergence: [0.00, 0.000] # orientation, position
    terminal: False
    use_metric: True
    run_weight: 1.0
  link_pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [30, 50, 10, 10] #[20.0, 100.0]
    vec_convergence: [0.00, 0.000] # orientation, position
    terminal: False
    use_metric: True
  cspace_cfg:
    weight: 0.00
  bound_cfg:
    weight: 5000.0
    activation_distance: [0.05]
  primitive_collision_cfg:
    weight: 500.0 
    use_sweep: False
    classify: False
    activation_distance: 0.035
  self_collision_cfg:
    weight: 500.0
    classify: False


mppi:
  init_cov          : 1.0 #0.15 #.5 #.5
  gamma             : 1.0
  n_iters           : 4
  cold_start_n_iters: null
  step_size_mean    : 0.9
  step_size_cov     : 0.1
  beta              : 0.01
  alpha             : 1
  num_particles     : 25 #10000
  update_cov        : True
  cov_type          : "DIAG_A" # 
  kappa             : 0.01
  null_act_frac     : 0.0
  sample_mode       : 'MEAN'
  base_action       : 'REPEAT'
  squash_fn         : 'CLAMP'
  n_problems            : 1
  use_cuda_graph    : True
  seed              : 0
  store_debug       : False
  random_mean       : False
  sample_per_problem: True
  sync_cuda_time    : False
  use_coo_sparse    : True
  sample_params:
    fixed_samples: True
    sample_ratio: {'halton':1.0, 'halton-knot':0.0, 'random':0.0, 'random-knot':0.0}
    seed: 23
    filter_coeffs: [0.0, 0.0, 1.0]
    n_knots: 5
  debug_info:
    visual_traj       : null #'state_seq'
