<?xml version="1.0" ?>
<!-- This file was autogenerated but we have modified quite a bit
Authors: <AUTHORS>

Noise: 0.111576
Masses: 0.005, 0.125164, 0.131691, 0.0211922,
Rotor Inertia: 0.00386572, 0.00346965, 0.00433775, 0.00366413,
Viscous Friction: 0.0414019, 0.00587541, 0.010638, 0.0226948,
Coulomb Friction: 0.0523963, 0.0150275, 0.00616359, 0.0227036,
Mass Inertia:
5.1458e-05, 5.1458e-05, 6.125e-05, 0, 0, 0,
6.39979e-06, 8.88687e-05, 9.13751e-05, -3.26531e-06, 1.23963e-05, 2.07384e-05,
7.04217e-05, 3.95744e-05, 6.61125e-05, -9.64342e-05, 5.8796e-05, -3.62996e-05,
2.93743e-05, 7.21391e-05, 7.59731e-05, -3.51896e-05, -6.31225e-05, -9.25392e-07,

Center of Mass:
0, 0, 0,
0.027, 0, 0,
0.038, 0, 0,
0.029, 0, 0,

-->

<robot name="iiwa_allegro" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <material name="Black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="Blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="Green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="Grey">
    <color rgba="0.4 0.4 0.4 1.0"/>
  </material>
  <material name="Orange">
    <color rgba="1.0 0.423529411765 0.0392156862745 1.0"/>
  </material>
  <material name="Brown">
    <color rgba="0.870588235294 0.811764705882 0.764705882353 1.0"/>
  </material>
  <material name="Red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="White">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <link name="base_link"/>
  <!--joint between {parent} and link_0-->
  <joint name="iiwa7_base_link_iiwa7_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="iiwa7_link_0"/>
  </joint>
  <link name="iiwa7_link_0">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.07"/>
      <mass value="5"/>
      <inertia ixx="0.05" ixy="0" ixz="0" iyy="0.06" iyz="0" izz="0.03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_0.obj"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_0.obj"/>
      </geometry>
      <material name="Grey"/>
    </collision>
    <self_collision_checking>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <capsule length="0.25" radius="0.15"/>
      </geometry>
    </self_collision_checking>
  </link>
  <!-- joint between link_0 and link_1 -->
  <joint name="iiwa7_joint_1" type="revolute">
    <parent link="iiwa7_link_0"/>
    <child link="iiwa7_link_1"/>
    <origin rpy="0 0 0" xyz="0 0 0.15"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_1">
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.03 0.12"/>
      <mass value="3.4525"/>
      <inertia ixx="0.02183" ixy="0" ixz="0" iyy="0.007703" iyz="-0.003887" izz="0.02083"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.0075"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_1.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.0075"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_1.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_2" type="revolute">
    <parent link="iiwa7_link_1"/>
    <child link="iiwa7_link_2"/>
    <origin rpy="1.57079632679   0 3.14159265359" xyz="0 0 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="5.55" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_2">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0003 0.059 0.042"/>
      <mass value="3.4821"/>
      <inertia ixx="0.02076" ixy="0" ixz="-0.003626" iyy="0.02179" iyz="0" izz="0.00779"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_2.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_2.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_3" type="revolute">
    <parent link="iiwa7_link_2"/>
    <child link="iiwa7_link_3"/>
    <origin rpy="1.57079632679 0 3.14159265359" xyz="0 0.21 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_3">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.03 0.13"/>
      <mass value="4.05623"/>
      <inertia ixx="0.03204" ixy="0" ixz="0" iyy="0.00972" iyz="0.006227" izz="0.03042"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_3.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_3.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_4" type="revolute">
    <parent link="iiwa7_link_3"/>
    <child link="iiwa7_link_4"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_4">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.067 0.034"/>
      <mass value="3.4822"/>
      <inertia ixx="0.02178" ixy="0" ixz="0" iyy="0.02075" iyz="-0.003625" izz="0.007785"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_4.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_4.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_5" type="revolute">
    <parent link="iiwa7_link_4"/>
    <child link="iiwa7_link_5"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.21 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_5">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0001 0.021 0.076"/>
      <mass value="2.1633"/>
      <inertia ixx="0.01287" ixy="0" ixz="0" iyy="0.005708" iyz="-0.003946" izz="0.01112"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_5.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_5.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_6" type="revolute">
    <parent link="iiwa7_link_5"/>
    <child link="iiwa7_link_6"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0.06070 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="0.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_6">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.0006 0.0004"/>
      <mass value="2.3466"/>
      <inertia ixx="0.006509" ixy="0" ixz="0" iyy="0.006259" iyz="0.00031891" izz="0.004527"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_6.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_6.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_7" type="revolute">
    <parent link="iiwa7_link_6"/>
    <child link="iiwa7_link_7"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.081 0.06070"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-3.05432619099" upper="3.05432619099" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-3.01941960595" soft_upper_limit="3.01941960595"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_7">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0.02"/>
      <mass value="3.129"/>
      <inertia ixx="0.01464" ixy="0.0005912" ixz="0" iyy="0.01465" iyz="0" izz="0.002872"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.0005"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_7.obj"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.0005"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_7.obj"/>
      </geometry>
      <material name="Grey"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_ee" type="fixed">
    <parent link="iiwa7_link_7"/>
    <child link="iiwa7_link_ee"/>
    <origin rpy="0 0 0" xyz="0 0 0.071"/>
    <dynamics damping="5.5" friction="0.025"/>
  </joint>
  <link name="iiwa7_link_ee">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0.0"/>
      <mass value="0.1"/>
      <inertia ixx="1e-3" ixy="0.0" ixz="0" iyy="1e-3" iyz="0" izz="1e-3"/>
    </inertial>
  </link>
  
  <!-- Load Gazebo lib and set the robot namespace -->
  <!-- <gazebo>
      <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
        <robotNamespace>/${robot_name}</robotNamespace>
      </plugin>
    </gazebo> -->
  <!-- Link0 -->
  <gazebo reference="iiwa7_link_0">
    <material>Gazebo/Grey</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link1 -->
  <gazebo reference="iiwa7_link_1">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link2 -->
  <gazebo reference="iiwa7_link_2">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link3 -->
  <gazebo reference="iiwa7_link_3">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link4 -->
  <gazebo reference="iiwa7_link_4">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link5 -->
  <gazebo reference="iiwa7_link_5">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link6 -->
  <gazebo reference="iiwa7_link_6">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link7 -->
  <gazebo reference="iiwa7_link_7">
    <material>Gazebo/Grey</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <transmission name="iiwa7_tran_1">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_1">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_1">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_2">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_2">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_2">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_3">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_3">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_3">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_4">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_4">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_4">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_5">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_5">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_5">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_6">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_6">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_6">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_7">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_7">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_7">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <!-- ======================== BASE PARAMS ========================= -->
  <!-- ======================== FINGER PARAMS ======================== -->
  <!-- full height from joint to tip. when used,
       the radius of the finger tip sphere will be subtracted
       and one fixed link will be added for the tip. -->
  <!-- ========================= THUMB PARAMS ========================= -->
  <!-- ========================= LIMITS ========================= -->
  <!-- ============================================================================= -->
  <!-- BASE -->

  <link name="allegro_mount">
    <inertial>
      <mass value="0.05"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1e-4" ixy="0" ixz="0" iyy="1e-4" iyz="0" izz="1e-4"/>
    </inertial>
    <visual>
      <!-- <origin xyz="-0.0425 -0.0425 0" rpy="0 0 0" /> -->
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/mounts/allegro_mount.obj"/>
      </geometry>
      <material name="color_j7"/>
    </visual>
    <collision>
      <!-- <origin xyz="-0.0425 -0.0425 0" rpy="0 0 0" /> -->
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/mounts/allegro_mount.obj"/>
      </geometry>
    </collision>
  </link>
  <!-- <joint name="allegro_mount_joint1" type="fixed">
    
    <origin rpy="0 0 0" xyz="-0.00 -0.0 0.0"/>
    <parent link="iiwa7_link_ee"/>
    <child link="allegro_mount"/>

  </joint> -->
  <joint name="allegro_mount_joint" type="fixed">
    <!-- <origin xyz="0.065 0 0.0275" rpy="0 1.57 0" /> -->
    <origin rpy="0 -1.5708 0.785398" xyz="0.008219 -0.02063 0.08086"/>
    <parent link="allegro_mount"/>
    <child link="palm_link"/>

  </joint>
  
  <!-- BASE -->
  <link name="palm_link">
    <inertial>
      <mass value="0.4154"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1e-4" ixy="0" ixz="0" iyy="1e-4" iyz="0" izz="1e-4"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/base_link.obj"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0 "/>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0.0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/allegro/base_link.obj"/>
      </geometry>
    </collision>
  </link>
  <gazebo reference="palm_link">
    <material value="Gazebo/Grey"/>
  </gazebo>
  <!-- ============================================================================= -->
  <!-- FINGERS -->
  <!-- RIGHT HAND due to which finger is number 0 -->
  <!-- for LEFT HAND switch the sign of the **offset_origin_y** and **finger_angle_r** parameters-->
  <!-- [LINK 0, 4, 8] -->
  <link name="index_link_0">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.005"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="5.1458e-05" iyy="5.1458e-05" izz="6.125e-05" ixy="0" ixz="0" iyz="0"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_1">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.125164"/>
      <origin xyz="0.027 0 0"/>
      <inertia ixx="6.39979e-06" iyy="8.88687e-05" izz="9.13751e-05" ixy="-3.26531e-06" ixz="1.23963e-05" iyz="2.07384e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_2">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.131691"/>
      <origin xyz="0.039 0 0"/>
      <inertia ixx="7.04217e-05" iyy="3.95744e-05" izz="6.61125e-05" ixy="-9.64342e-05" ixz="5.8796e-05" iyz="-3.62996e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_3">
    <collision>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0211922"/>
      <origin xyz="0.029 0 0"/>
      <inertia ixx="2.93743e-05" iyy="7.21391e-05" izz="7.59731e-05" ixy="-3.51896e-05" ixz="-6.31225e-05" iyz="-9.25392e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0. 0.5 0. 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_0">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.005"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="5.1458e-05" iyy="5.1458e-05" izz="6.125e-05" ixy="0" ixz="0" iyz="0"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_1">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.125164"/>
      <origin xyz="0.027 0 0"/>
      <inertia ixx="6.39979e-06" iyy="8.88687e-05" izz="9.13751e-05" ixy="-3.26531e-06" ixz="1.23963e-05" iyz="2.07384e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_2">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.131691"/>
      <origin xyz="0.039 0 0"/>
      <inertia ixx="7.04217e-05" iyy="3.95744e-05" izz="6.61125e-05" ixy="-9.64342e-05" ixz="5.8796e-05" iyz="-3.62996e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_3">
    <collision>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0211922"/>
      <origin xyz="0.029 0 0"/>
      <inertia ixx="2.93743e-05" iyy="7.21391e-05" izz="7.59731e-05" ixy="-3.51896e-05" ixz="-6.31225e-05" iyz="-9.25392e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_0">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.005"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="5.1458e-05" iyy="5.1458e-05" izz="6.125e-05" ixy="0" ixz="0" iyz="0"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_1">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.125164"/>
      <origin xyz="0.027 0 0"/>
      <inertia ixx="6.39979e-06" iyy="8.88687e-05" izz="9.13751e-05" ixy="-3.26531e-06" ixz="1.23963e-05" iyz="2.07384e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_2">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.131691"/>
      <origin xyz="0.039 0 0"/>
      <inertia ixx="7.04217e-05" iyy="3.95744e-05" izz="6.61125e-05" ixy="-9.64342e-05" ixz="5.8796e-05" iyz="-3.62996e-05"/>	
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_3">
    <collision>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.0211922"/>
      <origin xyz="0.029 0 0"/>
      <inertia ixx="2.93743e-05" iyy="7.21391e-05" izz="7.59731e-05" ixy="-3.51896e-05" ixz="-6.31225e-05" iyz="-9.25392e-07"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_0">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/thumb_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/thumb_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_1">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/thumb_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/thumb_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_2">
    <collision>
      <geometry>
        <mesh filename="meshes/allegro/thumb_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/allegro/thumb_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_3">
    <collision>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor_thumb.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="meshes/biotac/biotac_sensor_thumb.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="index_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 -0.0872638888889" xyz="0.0514302 -0.03632 -0.0113"/>
    <parent link="palm_link"/>
    <child link="index_link_0"/>
    <dynamics damping="0.0414019" friction="0.0523963"/>
  </joint>
  <joint name="index_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="index_link_0"/>
    <child link="index_link_1"/>
    <dynamics damping="0.00587541" friction="0.0150275"/>
  </joint>
  <joint name="index_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="index_link_1"/>
    <child link="index_link_2"/>
    <dynamics damping="0.010638" friction="0.00616359"/>
  </joint>
  <joint name="index_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="index_link_2"/>
    <child link="index_link_3"/>
    <dynamics damping="0.0226948" friction="0.0227036"/>
  </joint>
  <joint name="middle_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0" xyz="0.0537375 0.0087771 -0.0113"/>
    <parent link="palm_link"/>
    <child link="middle_link_0"/>
    <dynamics damping="0.0414019" friction="0.0523963"/>
  </joint>
  <joint name="middle_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="middle_link_0"/>
    <child link="middle_link_1"/>
    <dynamics damping="0.00587541" friction="0.0150275"/>
  </joint>
  <joint name="middle_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="middle_link_1"/>
    <child link="middle_link_2"/>
    <dynamics damping="0.010638" friction="0.00616359"/>
  </joint>
  <joint name="middle_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="middle_link_2"/>
    <child link="middle_link_3"/>
    <dynamics damping="0.0226948" friction="0.0227036"/>
  </joint>
  <joint name="ring_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0.0872638888889" xyz="0.0514302 0.0538749 -0.0113"/>
    <parent link="palm_link"/>
    <child link="ring_link_0"/>
    <dynamics damping="0.0414019" friction="0.0523963"/>
  </joint>
  <joint name="ring_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="ring_link_0"/>
    <child link="ring_link_1"/>
    <dynamics damping="0.00587541" friction="0.0150275"/>
  </joint>
  <joint name="ring_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="ring_link_1"/>
    <child link="ring_link_2"/>
    <dynamics damping="0.010638" friction="0.00616359"/>
  </joint>
  <joint name="ring_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="ring_link_2"/>
    <child link="ring_link_3"/>
    <dynamics damping="0.0226948" friction="0.0227036"/>
  </joint>
  <joint name="thumb_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="0.279244444444" upper="1.57075" velocity="6.283"/>
    <origin rpy="-1.57075 -1.57075 1.48348611111" xyz="-0.0367482 -0.0081281 -0.0295"/>
    <parent link="palm_link"/>
    <child link="thumb_link_0"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.331602777778" upper="1.15188333333" velocity="6.283"/>
    <origin rpy="1.57075 0 0" xyz="0.005 0.0 0.0"/>
    <parent link="thumb_link_0"/>
    <child link="thumb_link_1"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0.0" xyz="0 0 0.0554"/>
    <parent link="thumb_link_1"/>
    <child link="thumb_link_2"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.76273055556" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0514 0.0 0.0"/>
    <parent link="thumb_link_2"/>
    <child link="thumb_link_3"/>
    <dynamics friction="0.035"/>
  </joint>
  <!-- ============================================================================= -->

  <!-- Create a different *root* for the allegro hand -->
  <!-- Note: this offset is just eyeballed... -->
  <joint name="iiwa7_allegro" type="fixed">
    <parent link="iiwa7_link_ee"/>
    <child link="allegro_mount"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  
</robot>
