<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from lula_kuka_allegro.urdf.xacro   | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="kuka_iiwa" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <material name="Black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="Blue">
    <color rgba="0.0 0.0 0.8 1.0"/>
  </material>
  <material name="Green">
    <color rgba="0.0 0.8 0.0 1.0"/>
  </material>
  <material name="Grey">
    <color rgba="0.4 0.4 0.4 1.0"/>
  </material>
  <material name="Orange">
    <color rgba="1.0 0.423529411765 0.0392156862745 1.0"/>
  </material>
  <material name="Brown">
    <color rgba="0.870588235294 0.811764705882 0.764705882353 1.0"/>
  </material>
  <material name="Red">
    <color rgba="0.8 0.0 0.0 1.0"/>
  </material>
  <material name="White">
    <color rgba="1.0 1.0 1.0 1.0"/>
  </material>
  <link name="base_link"/>
  <!--joint between {parent} and link_0-->
  <joint name="iiwa7_base_link_iiwa7_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="base_link"/>
    <child link="iiwa7_link_0"/>
  </joint>
  <link name="iiwa7_link_0">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.07"/>
      <mass value="5"/>
      <inertia ixx="0.05" ixy="0" ixz="0" iyy="0.06" iyz="0" izz="0.03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_0.obj"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_0.obj"/>
      </geometry>
      <material name="Grey"/>
    </collision>
    <self_collision_checking>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <capsule length="0.25" radius="0.15"/>
      </geometry>
    </self_collision_checking>
  </link>
  <!-- joint between link_0 and link_1 -->
  <joint name="iiwa7_joint_1" type="revolute">
    <parent link="iiwa7_link_0"/>
    <child link="iiwa7_link_1"/>
    <origin rpy="0 0 0" xyz="0 0 0.15"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_1">
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.03 0.12"/>
      <mass value="3.4525"/>
      <inertia ixx="0.02183" ixy="0" ixz="0" iyy="0.007703" iyz="-0.003887" izz="0.02083"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.0075"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_1.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.0075"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_1.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_2" type="revolute">
    <parent link="iiwa7_link_1"/>
    <child link="iiwa7_link_2"/>
    <origin rpy="1.57079632679   0 3.14159265359" xyz="0 0 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_2">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0003 0.059 0.042"/>
      <mass value="3.4821"/>
      <inertia ixx="0.02076" ixy="0" ixz="-0.003626" iyy="0.02179" iyz="0" izz="0.00779"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_2.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_2.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_3" type="revolute">
    <parent link="iiwa7_link_2"/>
    <child link="iiwa7_link_3"/>
    <origin rpy="1.57079632679 0 3.14159265359" xyz="0 0.21 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_3">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.03 0.13"/>
      <mass value="4.05623"/>
      <inertia ixx="0.03204" ixy="0" ixz="0" iyy="0.00972" iyz="0.006227" izz="0.03042"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_3.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_3.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_4" type="revolute">
    <parent link="iiwa7_link_3"/>
    <child link="iiwa7_link_4"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_4">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.067 0.034"/>
      <mass value="3.4822"/>
      <inertia ixx="0.02178" ixy="0" ixz="0" iyy="0.02075" iyz="-0.003625" izz="0.007785"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_4.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_4.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_5" type="revolute">
    <parent link="iiwa7_link_4"/>
    <child link="iiwa7_link_5"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.21 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.93215314335" soft_upper_limit="2.93215314335"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_5">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0001 0.021 0.076"/>
      <mass value="2.1633"/>
      <inertia ixx="0.01287" ixy="0" ixz="0" iyy="0.005708" iyz="-0.003946" izz="0.01112"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_5.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.026"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_5.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_6" type="revolute">
    <parent link="iiwa7_link_5"/>
    <child link="iiwa7_link_6"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0.06070 0.19"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-2.05948851735" soft_upper_limit="2.05948851735"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_6">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.0006 0.0004"/>
      <mass value="2.3466"/>
      <inertia ixx="0.006509" ixy="0" ixz="0" iyy="0.006259" iyz="0.00031891" izz="0.004527"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_6.obj"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_6.obj"/>
      </geometry>
      <material name="Orange"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_7" type="revolute">
    <parent link="iiwa7_link_6"/>
    <child link="iiwa7_link_7"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.081 0.06070"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-3.05432619099" upper="3.05432619099" velocity="10"/>
    <safety_controller k_position="100" k_velocity="2" soft_lower_limit="-3.01941960595" soft_upper_limit="3.01941960595"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="iiwa7_link_7">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0.02"/>
      <mass value="3.129"/>
      <inertia ixx="0.01464" ixy="0.0005912" ixz="0" iyy="0.01465" iyz="0" izz="0.002872"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.0005"/>
      <geometry>
        <mesh filename="meshes/iiwa7/visual/link_7.obj"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.0005"/>
      <geometry>
        <mesh filename="meshes/iiwa7/collision/link_7.obj"/>
      </geometry>
      <material name="Grey"/>
    </collision>
  </link>
  <joint name="iiwa7_joint_ee" type="fixed">
    <parent link="iiwa7_link_7"/>
    <child link="iiwa7_link_ee"/>
    <origin rpy="0 0 0" xyz="0 0 0.071"/>
  </joint>
  <link name="iiwa7_link_ee">
    </link>
  <!-- Load Gazebo lib and set the robot namespace -->
  <!-- <gazebo>
      <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
        <robotNamespace>/${robot_name}</robotNamespace>
      </plugin>
    </gazebo> -->
  <!-- Link0 -->
  <gazebo reference="iiwa7_link_0">
    <material>Gazebo/Grey</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link1 -->
  <gazebo reference="iiwa7_link_1">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link2 -->
  <gazebo reference="iiwa7_link_2">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link3 -->
  <gazebo reference="iiwa7_link_3">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link4 -->
  <gazebo reference="iiwa7_link_4">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link5 -->
  <gazebo reference="iiwa7_link_5">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link6 -->
  <gazebo reference="iiwa7_link_6">
    <material>Gazebo/Orange</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <!-- Link7 -->
  <gazebo reference="iiwa7_link_7">
    <material>Gazebo/Grey</material>
    <mu1>0.2</mu1>
    <mu2>0.2</mu2>
  </gazebo>
  <transmission name="iiwa7_tran_1">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_1">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_1">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_2">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_2">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_2">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_3">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_3">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_3">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_4">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_4">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_4">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_5">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_5">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_5">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_6">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_6">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_6">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="iiwa7_tran_7">
    <robotNamespace>/iiwa7</robotNamespace>
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="iiwa7_joint_7">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="iiwa7_motor_7">
      <hardwareInterface>PositionJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
</robot>
