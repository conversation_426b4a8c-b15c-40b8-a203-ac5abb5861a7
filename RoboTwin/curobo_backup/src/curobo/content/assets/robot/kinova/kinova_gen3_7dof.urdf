<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from robots/gen3_robotiq_2f_85.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="gen3_robotiq_2f_85">
  <!-- Run the macros -->
  <!-- For gazebo and DART -->

  <link name="base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.000648 -0.000166 0.084487"/>
      <mass value="1.697"/>
      <inertia ixx="0.004622" ixy="9E-06" ixz="6E-05" iyy="0.004495" iyz="9E-06" izz="0.002079"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/base_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/base_link.STL"/>
      </geometry>
    </collision>
  </link>
  <link name="shoulder_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-2.3E-05 -0.010364 -0.07336"/>
      <mass value="1.3773"/>
      <inertia ixx="0.00457" ixy="1E-06" ixz="2E-06" iyy="0.004831" iyz="0.000448" izz="0.001409"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/shoulder_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/shoulder_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_1" type="revolute">
    <origin rpy="3.1416 2.7629E-18 -4.9305E-36" xyz="0 0 0.15643"/>
    <parent link="base_link"/>
    <child link="shoulder_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="39" lower="-6.0" upper="6.0" velocity="1.3963"/>
  </joint>
  <link name="half_arm_1_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-4.4E-05 -0.09958 -0.013278"/>
      <mass value="1.1636"/>
      <inertia ixx="0.011088" ixy="5E-06" ixz="0" iyy="0.001072" iyz="-0.000691" izz="0.011255"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/half_arm_1_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/half_arm_1_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_2" type="revolute">
    <origin rpy="1.5708 2.1343E-17 -1.1102E-16" xyz="0 0.005375 -0.12838"/>
    <parent link="shoulder_link"/>
    <child link="half_arm_1_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="39" lower="-2.41" upper="2.41" velocity="1.3963"/>
  </joint>
  <link name="half_arm_2_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-4.4E-05 -0.006641 -0.117892"/>
      <mass value="1.1636"/>
      <inertia ixx="0.010932" ixy="0" ixz="-7E-06" iyy="0.011127" iyz="0.000606" izz="0.001043"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/half_arm_2_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/half_arm_2_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_3" type="revolute">
    <origin rpy="-1.5708 1.2326E-32 -2.9122E-16" xyz="0 -0.21038 -0.006375"/>
    <parent link="half_arm_1_link"/>
    <child link="half_arm_2_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="39"  lower="-6.0" upper="6.0" velocity="1.3963"/>
  </joint>
  <link name="forearm_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-1.8E-05 -0.075478 -0.015006"/>
      <mass value="0.9302"/>
      <inertia ixx="0.008147" ixy="-1E-06" ixz="0" iyy="0.000631" iyz="-0.0005" izz="0.008316"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/forearm_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/forearm_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_4" type="revolute">
    <origin rpy="1.5708 -6.6954E-17 -1.6653E-16" xyz="0 0.006375 -0.21038"/>
    <parent link="half_arm_2_link"/>
    <child link="forearm_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="39" lower="-2.66" upper="2.66" velocity="1.3963"/>
  </joint>
  <link name="spherical_wrist_1_link">
    <inertial>
      <origin rpy="0 0 0" xyz="1E-06 -0.009432 -0.063883"/>
      <mass value="0.6781"/>
      <inertia ixx="0.001596" ixy="0" ixz="0" iyy="0.001607" iyz="0.000256" izz="0.000399"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/spherical_wrist_1_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/spherical_wrist_1_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_5" type="revolute">
    <origin rpy="-1.5708 2.2204E-16 -6.373E-17" xyz="0 -0.20843 -0.006375"/>
    <parent link="forearm_link"/>
    <child link="spherical_wrist_1_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="9" lower="-6.0" upper="6.0"  velocity="1.2218"/>
  </joint>
  <link name="spherical_wrist_2_link">
    <inertial>
      <origin rpy="0 0 0" xyz="1E-06 -0.045483 -0.00965"/>
      <mass value="0.6781"/>
      <inertia ixx="0.001641" ixy="0" ixz="0" iyy="0.00041" iyz="-0.000278" izz="0.001641"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/spherical_wrist_2_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/spherical_wrist_2_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_6" type="revolute">
    <origin rpy="1.5708 9.2076E-28 -8.2157E-15" xyz="0 0.00017505 -0.10593"/>
    <parent link="spherical_wrist_1_link"/>
    <child link="spherical_wrist_2_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="9" lower="-2.23" upper="2.23" velocity="1.2218"/>
  </joint>
  <link name="bracelet_link">
    <inertial>
      <origin rpy="0 0 0" xyz="0.000281 0.011402 -0.029798"/>
      <mass value="0.5"/>
      <inertia ixx="0.000587" ixy="3E-06" ixz="3E-06" iyy="0.000369" iyz="-0.000118" izz="0.000609"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/bracelet_with_vision_link.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/arms/gen3/7dof/meshes/bracelet_with_vision_link.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_7" type="revolute">
    <origin rpy="-1.5708 -5.5511E-17 9.6396E-17" xyz="0 -0.10593 -0.00017505"/>
    <parent link="spherical_wrist_2_link"/>
    <child link="bracelet_link"/>
    <axis xyz="0 0 1"/>
    <limit effort="9"  lower="-6.0" upper="6.0" velocity="1.2218"/>
  </joint>
  <link name="end_effector_link"/>
  <joint name="end_effector" type="fixed">
    <origin rpy="3.14159265358979 1.09937075168372E-32 0" xyz="0 0 -0.0615250000000001"/>
    <parent link="bracelet_link"/>
    <child link="end_effector_link"/>
    <axis xyz="0 0 0"/>
  </joint>
  <link name="camera_link"/>
  <joint name="camera_module" type="fixed">
    <origin rpy="3.14159265358979 3.14159265358979 0" xyz="0 0.05639 -0.00305"/>
    <parent link="end_effector_link"/>
    <child link="camera_link"/>
  </joint>
  <link name="camera_depth_frame"/>
  <joint name="depth_module" type="fixed">
    <origin rpy="3.14159265358979 3.14159265358979 0" xyz="0.0275 0.066 -0.00305"/>
    <parent link="end_effector_link"/>
    <child link="camera_depth_frame"/>
  </joint>
  <link name="camera_color_frame"/>
  <joint name="color_module" type="fixed">
    <origin rpy="3.14159265358979 3.14159265358979 0" xyz="0 0.05639 -0.00305"/>
    <parent link="end_effector_link"/>
    <child link="camera_color_frame"/>
  </joint>
  <!-- This line was removed by Kinova because we replaced the transmission file with our own
    <xacro:include filename="$(find robotiq_2f_85_gripper_visualization)/urdf/robotiq_arg2f_transmission.xacro" /> -->
  <!-- Tool frame used by the arm -->
  <link name="tool_frame"/>
  <joint name="tool_frame_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.120"/>
    <parent link="end_effector_link"/>
    <child link="tool_frame"/>
    <axis xyz="0 0 0"/>
  </joint>
  <!-- This joint was added by Kinova -->
  <joint name="gripper_base_joint" type="fixed">
    <parent link="end_effector_link"/>
    <child link="robotiq_arg2f_base_link"/>
    <origin rpy="0.0 0.0 1.57" xyz="0.0 0.0 0.0"/>
  </joint>
  <link name="robotiq_arg2f_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="8.625E-08 -4.6583E-06 0.03145"/>
      <mass value="0.22652"/>
      <inertia ixx="0.00020005" ixy="-4.2442E-10" ixz="-2.9069E-10" iyy="0.00017832" iyz="-3.4402E-08" izz="0.00013478"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <!-- The DAE file doesn't show well in Gazebo so we're using the STL instead -->
        <!-- <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_base_link.dae" scale="0.001 0.001 0.001"/> -->
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_base_link.stl"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_base_link.stl"/>
      </geometry>
    </collision>
  </link>
  <gazebo reference="robotiq_arg2f_base_link">
    <material>Gazebo/Black</material>
  </gazebo>
  <link name="left_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.000200000000003065 0.0199435877845359 0.0292245259211331"/>
      <mass value="0.00853198276973456"/>
      <inertia ixx="2.89328108496468E-06" ixy="-1.57935047237397E-19" ixz="-1.93980378593255E-19" iyy="1.86719750325683E-06" iyz="-1.21858577871576E-06" izz="1.21905238907251E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="left_outer_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00030115855001899 0.0373907951953854 -0.0208027427000385"/>
      <mass value="0.022614240507152"/>
      <inertia ixx="1.52518312458174E-05" ixy="9.76583423954399E-10" ixz="-5.43838577022588E-10" iyy="6.17694243867776E-06" iyz="6.78636130740228E-06" izz="1.16494917907219E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.000299999999999317 0.0160078233491243 -0.0136945669206257"/>
      <mass value="0.0104003125914103"/>
      <inertia ixx="2.71909453810972E-06" ixy="1.35402465472579E-21" ixz="-7.1817349065269E-22" iyy="7.69100314106116E-07" iyz="6.74715432769696E-07" izz="2.30315190420171E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="left_inner_finger_pad">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.022 0.00635 0.0375"/>
      </geometry>
      <material name="">
        <color rgba="0.9 0.9 0.9 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.022 0.00635 0.0375"/>
      </geometry>
      <material name="">
        <color rgba="0.9 0.0 0.0 1"/>
      </material>
    </collision>
  </link>
  <link name="left_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.000123011831763771 0.0507850843201817 0.00103968640075166"/>
      <mass value="0.0271177346495152"/>
      <inertia ixx="2.61910379223783E-05" ixy="-2.43616858946494E-07" ixz="-6.37789906117123E-09" iyy="2.8270243746167E-06" iyz="-5.37200748039765E-07" izz="2.83695868220296E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="right_outer_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.000200000000003065 0.0199435877845359 0.0292245259211331"/>
      <mass value="0.00853198276973456"/>
      <inertia ixx="2.89328108496468E-06" ixy="-1.57935047237397E-19" ixz="-1.93980378593255E-19" iyy="1.86719750325683E-06" iyz="-1.21858577871576E-06" izz="1.21905238907251E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="right_outer_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00030115855001899 0.0373907951953854 -0.0208027427000385"/>
      <mass value="0.022614240507152"/>
      <inertia ixx="1.52518312458174E-05" ixy="9.76583423954399E-10" ixz="-5.43838577022588E-10" iyy="6.17694243867776E-06" iyz="6.78636130740228E-06" izz="1.16494917907219E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_outer_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_outer_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_finger">
    <inertial>
      <origin rpy="0 0 0" xyz="0.000299999999999317 0.0160078233491243 -0.0136945669206257"/>
      <mass value="0.0104003125914103"/>
      <inertia ixx="2.71909453810972E-06" ixy="1.35402465472579E-21" ixz="-7.1817349065269E-22" iyy="7.69100314106116E-07" iyz="6.74715432769696E-07" izz="2.30315190420171E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_finger.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <link name="right_inner_finger_pad">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.022 0.00635 0.0375"/>
      </geometry>
      <material name="">
        <color rgba="0.9 0.9 0.9 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.022 0.00635 0.0375"/>
      </geometry>
      <material name="">
        <color rgba="0.9 0.0 0.0 1"/>
      </material>
    </collision>
  </link>
  <link name="right_inner_knuckle">
    <inertial>
      <origin rpy="0 0 0" xyz="0.000123011831763771 0.0507850843201817 0.00103968640075166"/>
      <mass value="0.0271177346495152"/>
      <inertia ixx="2.61910379223783E-05" ixy="-2.43616858946494E-07" ixz="-6.37789906117123E-09" iyy="2.8270243746167E-06" iyz="-5.37200748039765E-07" izz="2.83695868220296E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/visual/robotiq_arg2f_85_inner_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kortex_description/grippers/robotiq_2f_85/meshes/collision/robotiq_arg2f_85_inner_knuckle.dae" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_outer_knuckle_joint" type="fixed">
    <origin rpy="0 0 3.1415" xyz="0 -0.0306011 0.054904"/>
    <parent link="robotiq_arg2f_base_link"/>
    <child link="left_outer_knuckle"/>
    <!--axis xyz="1 0 0"/>
    <limit effort="1000" lower="-0.0" upper="0.8" velocity="2.0"/-->
  </joint>
  <joint name="left_outer_finger_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0315 -0.0041"/>
    <parent link="left_outer_knuckle"/>
    <child link="left_outer_finger"/>
  </joint>
  <joint name="left_inner_knuckle_joint" type="fixed">
    <origin rpy="0 0 3.141592653589793" xyz="0 -0.0127 0.06142"/>
    <parent link="robotiq_arg2f_base_link"/>
    <child link="left_inner_knuckle"/>
    
  </joint>
  <joint name="left_inner_finger_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0061 0.0471"/>
    <parent link="left_outer_finger"/>
    <child link="left_inner_finger"/>

  </joint>
  <joint name="left_inner_finger_pad_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.0220203446692936 0.03242"/>
    <parent link="left_inner_finger"/>
    <child link="left_inner_finger_pad"/>
  </joint>
  <joint name="right_outer_knuckle_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0306011 0.054904"/>
    <parent link="robotiq_arg2f_base_link"/>
    <child link="right_outer_knuckle"/>
    <axis xyz="1 0 0"/>
    <limit effort="1000" lower="0" upper="0.8" velocity="2.0"/>
  </joint>
  <joint name="right_outer_finger_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0315 -0.0041"/>
    <parent link="right_outer_knuckle"/>
    <child link="right_outer_finger"/>
  </joint>
  <joint name="right_inner_knuckle_joint" type="fixed">
    <origin rpy="0 0 0.0" xyz="0 0.0127 0.06142"/>
    <parent link="robotiq_arg2f_base_link"/>
    <child link="right_inner_knuckle"/>
    
  </joint>
  <joint name="right_inner_finger_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 0.0061 0.0471"/>
    <parent link="right_outer_finger"/>
    <child link="right_inner_finger"/>
    
  </joint>
  <joint name="right_inner_finger_pad_joint" type="fixed">
    <origin rpy="0 0 0" xyz="0 -0.0220203446692936 0.03242"/>
    <parent link="right_inner_finger"/>
    <child link="right_inner_finger_pad"/>
  </joint>
</robot>