<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from j2n7s300_standalone.xacro      | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<!-- j2n7s300 refers to jaco v2 7DOF non-spherical service 3fingers -->
<robot name="j2n7s300" xmlns:body="http://playerstage.sourceforge.net/gazebo/xmlschema/#body" xmlns:controller="http://playerstage.sourceforge.net/gazebo/xmlschema/#controller" xmlns:gazebo="http://playerstage.sourceforge.net/gazebo/xmlschema/#gz" xmlns:geom="http://playerstage.sourceforge.net/gazebo/xmlschema/#geom" xmlns:interface="http://playerstage.sourceforge.net/gazebo/xmlschema/#interface" xmlns:joint="http://playerstage.sourceforge.net/gazebo/xmlschema/#joint" xmlns:model="http://playerstage.sourceforge.net/gazebo/xmlschema/#model" xmlns:physics="http://playerstage.sourceforge.net/gazebo/xmlschema/#physics" xmlns:renderable="http://playerstage.sourceforge.net/gazebo/xmlschema/#renderable" xmlns:rendering="http://playerstage.sourceforge.net/gazebo/xmlschema/#rendering" xmlns:sensor="http://playerstage.sourceforge.net/gazebo/xmlschema/#sensor" xmlns:xi="http://www.w3.org/2001/XInclude">
  <!-- links      		mesh_no
   base           		0
   shoulder       		1
   arm            		2
   forearm        		3
   wrist          		4
   arm_mico       		5
   arm_half1 (7dof)		6
   arm_half2 (7dof)		7
   wrist_spherical_1  8
   wrist_spherical_2  9

   hand 3 finger  		55
   hand_2finger   		56
   finger_proximal		57
   finger_distal      58
-->
  <!-- links      		mesh_no
   base           		0
   shoulder       		1
   arm            		2
   forearm        		3
   wrist          		4
   arm_mico       		5
   arm_half1 (7dof)		6
   arm_half2 (7dof)		7
   wrist_spherical_1  8
   wrist_spherical_2  9

   hand 3 finger  		55
   hand_2finger   		56
   finger_proximal		57
   finger_distal      58
-->
  <link name="root"/>
  <!-- for gazebo -->
  <link name="world"/>
  <joint name="connect_root_and_world" type="fixed">
    <child link="root"/>
    <parent link="world"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <!-- ros_control plugin -->
  <gazebo>
    <plugin filename="libgazebo_ros_control.so" name="gazebo_ros_control">
      <robotNamespace>j2n7s300</robotNamespace>
      <robotSimType>gazebo_ros_control/DefaultRobotHWSim</robotSimType>
      <legacyModeNS>true</legacyModeNS>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_base">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/base.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/base.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.46784"/>
      <origin rpy="0 0 0" xyz="0 0 0.1255"/>
      <inertia ixx="0.000951270861568" ixy="0" ixz="0" iyy="0.000951270861568" iyz="0" izz="0.00037427200000000004"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_base" type="fixed">
    <parent link="root"/>
    <child link="j2n7s300_link_base"/>
    <axis xyz="0 0 0"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="j2n7s300_link_1">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/shoulder.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/shoulder.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.7477"/>
      <origin xyz="0 -0.002 -0.0605"/>
      <inertia ixx="0.0015203172520400004" ixy="0" ixz="0" iyy="0.0015203172520400004" iyz="0" izz="0.00059816"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_1" type="continuous">
    <parent link="j2n7s300_link_base"/>
    <child link="j2n7s300_link_1"/>
    <axis xyz="0 0 1"/>
    <limit effort="40" velocity="0.6283185307179586"/>
    <origin rpy="0 3.141592653589793 0" xyz="0 0 0.15675"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_1_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_1">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_1_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_1">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_1_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_1</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_2">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/arm_half_1.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/arm_half_1.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.8447"/>
      <origin xyz="0 -0.103563213 0"/>
      <inertia ixx="0.00247073761701" ixy="0" ixz="0" iyy="0.000380115" iyz="0" izz="0.00247073761701"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_2" type="revolute">
    <parent link="j2n7s300_link_1"/>
    <child link="j2n7s300_link_2"/>
    <axis xyz="0 0 1"/>
    <limit effort="80" lower="0.5235987755982988" upper="5.759586531581287" velocity="0.6283185307179586"/>
    <origin rpy="-1.5707963267948966 0 3.141592653589793" xyz="0 0.0016 -0.11875"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_2_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_2">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_2_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_2">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_2_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_2</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_3">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/arm_half_2.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/arm_half_2.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.8447"/>
      <origin xyz="0 0 -0.1022447445"/>
      <inertia ixx="0.00247073761701" ixy="0" ixz="0" iyy="0.00247073761701" iyz="0" izz="0.000380115"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_3" type="continuous">
    <parent link="j2n7s300_link_2"/>
    <child link="j2n7s300_link_3"/>
    <axis xyz="0 0 1"/>
    <limit effort="40" velocity="0.6283185307179586"/>
    <origin rpy="1.5707963267948966 0 3.141592653589793" xyz="0 -0.205 0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_3_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_3">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_3_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_3">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_3_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_3</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_4">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/forearm.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_big.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/forearm.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.6763"/>
      <origin xyz="0 0.081 -0.0086"/>
      <inertia ixx="0.0014202243190800001" ixy="0" ixz="0" iyy="0.000304335" iyz="0" izz="0.0014202243190800001"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_4" type="revolute">
    <parent link="j2n7s300_link_3"/>
    <child link="j2n7s300_link_4"/>
    <axis xyz="0 0 1"/>
    <limit effort="40" lower="0.5235987755982988" upper="5.759586531581287" velocity="0.6283185307179586"/>
    <origin rpy="1.5707963267948966 0 3.141592653589793" xyz="0 0 -0.205"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_4_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_4">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_4_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_4">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_4_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_4</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_5">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/wrist.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/wrist.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.426367"/>
      <origin xyz="0 -0.037 -0.0642"/>
      <inertia ixx="7.734969059999999e-05" ixy="0" ixz="0" iyy="7.734969059999999e-05" iyz="0" izz="0.0001428"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_5" type="continuous">
    <parent link="j2n7s300_link_4"/>
    <child link="j2n7s300_link_5"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781"/>
    <origin rpy="-1.5707963267948966 0 3.141592653589793" xyz="0 0.2073 -0.0114"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_5_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_5">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_5_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_5">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_5_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_5</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_6">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/wrist.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/wrist.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.426367"/>
      <origin xyz="0 -0.037 -0.0642"/>
      <inertia ixx="7.734969059999999e-05" ixy="0" ixz="0" iyy="7.734969059999999e-05" iyz="0" izz="0.0001428"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_6" type="continuous">
    <parent link="j2n7s300_link_5"/>
    <child link="j2n7s300_link_6"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781"/>
    <origin rpy="1.0471975511965976 0 3.141592653589793" xyz="0 -0.03703 -0.06414"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_6_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_6">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_6_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_6">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_6_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_6</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_link_7">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/hand_3finger.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/ring_small.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/hand_3finger.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.99"/>
      <origin xyz="0 0 -0.06"/>
      <inertia ixx="0.00034532361869999995" ixy="0" ixz="0" iyy="0.00034532361869999995" iyz="0" izz="0.0005815999999999999"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_7" type="continuous">
    <parent link="j2n7s300_link_6"/>
    <child link="j2n7s300_link_7"/>
    <axis xyz="0 0 1"/>
    <limit effort="20" velocity="0.8377580409572781"/>
    <origin rpy="1.0471975511965976 0 3.141592653589793" xyz="0 -0.03703 -0.06414"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <transmission name="j2n7s300_joint_7_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_7">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_7_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>160</mechanicalReduction>
    </actuator>
  </transmission>
  <!--For torque sensing in simulation-->
  <gazebo reference="j2n7s300_joint_7">
    <provideFeedback>true</provideFeedback>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_ft_sensor.so" name="ft_sensor">
      <updateRate>30.0</updateRate>
      <topicName>j2n7s300_joint_7_ft_sensor_topic</topicName>
      <jointName>j2n7s300_joint_7</jointName>
    </plugin>
  </gazebo>
  <link name="j2n7s300_end_effector"/>
  <joint name="j2n7s300_joint_end_effector" type="fixed">
    <parent link="j2n7s300_link_7"/>
    <child link="j2n7s300_end_effector"/>
    <axis xyz="0 0 0"/>
    <origin rpy="3.141592653589793 0 1.5707963267948966" xyz="0 0 -0.1600"/>
  </joint>
  <link name="j2n7s300_link_finger_1">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_1" type="revolute">
    <parent link="j2n7s300_link_7"/>
    <child link="j2n7s300_link_finger_1"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.7047873384941834 0.6476144647144773 1.67317415161155" xyz="0.00279 0.03126 -0.11467"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_1_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_1">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_1_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="j2n7s300_link_finger_tip_1">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_tip_1" type="revolute">
    <parent link="j2n7s300_link_finger_1"/>
    <child link="j2n7s300_link_finger_tip_1"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_tip_1_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_tip_1">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_tip_1_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="j2n7s300_link_finger_2">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_2" type="revolute">
    <parent link="j2n7s300_link_7"/>
    <child link="j2n7s300_link_finger_2"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.570796327 .649262481663582 -1.38614049188413" xyz="0.02226 -0.02707 -0.11482"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_2_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_2">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_2_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="j2n7s300_link_finger_tip_2">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_tip_2" type="revolute">
    <parent link="j2n7s300_link_finger_2"/>
    <child link="j2n7s300_link_finger_tip_2"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_tip_2_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_tip_2">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_tip_2_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="j2n7s300_link_finger_3">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_proximal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_3" type="revolute">
    <parent link="j2n7s300_link_7"/>
    <child link="j2n7s300_link_finger_3"/>
    <axis xyz="0 0 1"/>
    <origin rpy="-1.570796327 .649262481663582 -1.75545216211587" xyz="-0.02226 -0.02707 -0.11482"/>
    <limit effort="2" lower="0" upper="1.51" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_3_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_3">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_3_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <link name="j2n7s300_link_finger_tip_3">
    <visual>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
      <material name="carbon_fiber">
        <color rgba="0.792156862745098 0.819607843137255 0.933333333333333 1"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://kinova_description/meshes/finger_distal.dae"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.01"/>
      <origin xyz="0.022 0 0"/>
      <inertia ixx="7.8999684e-07" ixy="0" ixz="0" iyy="7.8999684e-07" iyz="0" izz="8e-08"/>
    </inertial>
  </link>
  <joint name="j2n7s300_joint_finger_tip_3" type="revolute">
    <parent link="j2n7s300_link_finger_3"/>
    <child link="j2n7s300_link_finger_tip_3"/>
    <axis xyz="0 0 1"/>
    <origin rpy="0 0 0" xyz="0.044 -0.003 0"/>
    <limit effort="2" lower="0" upper="2" velocity="1"/>
  </joint>
  <transmission name="j2n7s300_joint_finger_tip_3_trans">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="j2n7s300_joint_finger_tip_3">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
    </joint>
    <actuator name="j2n7s300_joint_finger_tip_3_actuator">
      <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
</robot>

