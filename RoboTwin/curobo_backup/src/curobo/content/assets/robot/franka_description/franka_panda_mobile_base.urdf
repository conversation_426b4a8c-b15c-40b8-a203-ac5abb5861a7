<?xml version="1.0" ?>
<robot name="panda" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <link name="base_link"/>
  <link name="base_link_x"/>
  <link name="base_link_y"/>

<joint name="base_x" type="prismatic">
    <origin rpy="0 0 0.0" xyz="0 0 0.0"/>
    <parent link="base_link"/>
    <child link="base_link_x"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-5.0" upper="5.0" velocity="1.0"/>
  </joint>

<joint name="base_y" type="prismatic">
    <origin rpy="0 0 0.0" xyz="0 0 0.0"/>
    <parent link="base_link_x"/>
    <child link="base_link_y"/>
    <axis xyz="0 1 0"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-5.0" upper="5.0" velocity="1.0"/>
  </joint>

<joint name="base_z" type="revolute">
    <origin rpy="0 0 0.0" xyz="0 0 0.0"/>
    <parent link="base_link_y"/>
    <child link="panda_link0"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-6.0" upper="6.0" velocity="1.50"/>
  </joint>

  


  <link name="panda_link0">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link0.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link0.obj"/>
      </geometry>
    </collision>
    
  </link>
</robot>


