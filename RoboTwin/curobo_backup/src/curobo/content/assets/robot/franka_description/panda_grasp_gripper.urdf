<?xml version="1.0"?>
<robot name="panda_gripper">
  <link name="gripper">
    <visual>
      <origin rpy="0.0 0.0 0.0" xyz="0 0 0.0"/>
      <geometry>
        <mesh filename="meshes/visual/graspnet_panda_mesh.obj"/>

      </geometry>
    </visual>
    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="0 0 0.0"/>
      <geometry>
        <!--box size="0.01 0.01 0.01"/-->
        
        <mesh filename="meshes/visual/graspnet_panda_mesh.obj"/>
        <!--mesh filename="meshes/collision/hand_gripper.obj"/-->
      </geometry>
    </collision>

    <inertial>
      <mass value="0.102"/>
      <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
    </inertial>
  </link>
</robot>
