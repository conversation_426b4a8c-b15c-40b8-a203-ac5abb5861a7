#!/usr/bin/env python3
import sys
import yaml
sys.path.append('.')

print("最小测试开始...")

try:
    # 读取配置
    with open('task_config/blocks_stack_three.yml', 'r') as f:
        config = yaml.safe_load(f)
    print("✅ 配置读取成功")
    
    from envs.blocks_stack_three import blocks_stack_three
    env = blocks_stack_three()
    print("✅ 环境创建成功")
    
    # 读取机器人配置
    with open('assets/embodiments/aloha-agilex-1/config.yml', 'r') as f:
        robot_config = yaml.safe_load(f)
    
    # 最简化的参数
    args = {
        'task_name': 'blocks_stack_three',
        'render_freq': 0,
        'need_plan': False,
        'is_save': False,
        'save_path': './test_data',
        'embodiment_name': 'test',
        'setting': 'test',
        'dual_arm_embodied': True,
        'left_embodiment_config': robot_config,
        'right_embodiment_config': robot_config,
        'left_robot_file': 'assets/embodiments/aloha-agilex-1',
        'right_robot_file': 'assets/embodiments/aloha-agilex-1',
        'augmentation': {
            'messy_table': False,
            'random_background': False,
            'random_light': False,
            'random_table_height': 0,
            'random_head_camera_dis': 0,
        },
        'camera': {
            'head_camera_type': 'D435',
            'wrist_camera_type': 'D435',
            'collect_head_camera': False,
            'collect_wrist_camera': False,
        },
        'data_type': {
            'rgb': False,
            'depth': False,
            'observer': False,
            'endpose': False,
            'qpos': True,
        },
    }
    
    print("开始setup_demo...")
    env.setup_demo(now_ep_num=0, seed=42, **args)
    print("✅ setup_demo成功")
    
    print("✅ 最小测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
