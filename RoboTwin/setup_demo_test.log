/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/_vulkan_tricks.py:37: UserWarning: Failed to find Vulkan ICD file. This is probably due to an incorrect or partial installation of the NVIDIA driver. SAPIEN will attempt to provide an ICD file anyway but it may not work.
  warn(
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/test_setup_demo.py", line 51, in <module>
    env.setup_demo(now_ep_num=0, seed=42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/blocks_stack_three.py", line 9, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 141, in setup_scene
    self.engine = sapien.Engine()
UnboundLocalError: local variable 'sapien' referenced before assignment
测试setup_demo方法...
✅ 配置读取成功
[Planner.py]: Curobo not installed! No module named 'curobo'
✅ 环境创建成功
开始setup_demo...
❌ 测试失败: local variable 'sapien' referenced before assignment
