/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree.
 */

// clang-format off

#include <torch/extension.h>
#include "sample_farthest_points/sample_farthest_points.h"


PYBIND11_MODULE(TORCH_EXTENSION_NAME, m) {
  
  m.def("sample_farthest_points", &FarthestPointSampling);
}
