*.7z filter=lfs diff=lfs merge=lfs -text
*.arrow filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.bz2 filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text
*.ftz filter=lfs diff=lfs merge=lfs -text
*.gz filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.joblib filter=lfs diff=lfs merge=lfs -text
*.lfs.* filter=lfs diff=lfs merge=lfs -text
*.lz4 filter=lfs diff=lfs merge=lfs -text
*.mds filter=lfs diff=lfs merge=lfs -text
*.mlmodel filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text
*.msgpack filter=lfs diff=lfs merge=lfs -text
*.npy filter=lfs diff=lfs merge=lfs -text
*.npz filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text
*.ot filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
*.pb filter=lfs diff=lfs merge=lfs -text
*.pickle filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.safetensors filter=lfs diff=lfs merge=lfs -text
saved_model/**/* filter=lfs diff=lfs merge=lfs -text
*.tar.* filter=lfs diff=lfs merge=lfs -text
*.tar filter=lfs diff=lfs merge=lfs -text
*.tflite filter=lfs diff=lfs merge=lfs -text
*.tgz filter=lfs diff=lfs merge=lfs -text
*.wasm filter=lfs diff=lfs merge=lfs -text
*.xz filter=lfs diff=lfs merge=lfs -text
*.zip filter=lfs diff=lfs merge=lfs -text
*.zst filter=lfs diff=lfs merge=lfs -text
*tfevents* filter=lfs diff=lfs merge=lfs -text
# Audio files - uncompressed
*.pcm filter=lfs diff=lfs merge=lfs -text
*.sam filter=lfs diff=lfs merge=lfs -text
*.raw filter=lfs diff=lfs merge=lfs -text
# Audio files - compressed
*.aac filter=lfs diff=lfs merge=lfs -text
*.flac filter=lfs diff=lfs merge=lfs -text
*.mp3 filter=lfs diff=lfs merge=lfs -text
*.ogg filter=lfs diff=lfs merge=lfs -text
*.wav filter=lfs diff=lfs merge=lfs -text
# Image files - uncompressed
*.bmp filter=lfs diff=lfs merge=lfs -text
*.gif filter=lfs diff=lfs merge=lfs -text
*.png filter=lfs diff=lfs merge=lfs -text
*.tiff filter=lfs diff=lfs merge=lfs -text
# Image files - compressed
*.jpg filter=lfs diff=lfs merge=lfs -text
*.jpeg filter=lfs diff=lfs merge=lfs -text
*.webp filter=lfs diff=lfs merge=lfs -text
# Video files - compressed
*.mp4 filter=lfs diff=lfs merge=lfs -text
*.webm filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/base_link.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/box2_Link.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/box2_Link.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/d435.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/d435.dae.backup filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link1.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link2.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link3.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link4.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link5.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/link6.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/tracer_base_link.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/meshes/tracer_base_link_no_wheel.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/back_link7.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/base_link.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/box2_Link.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/box2_Link.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/camera_link1.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/camera_link1.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/camera_link2.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/camera_link2.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/d435.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/d435_back.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/d435_back1.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link1.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link2.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link3.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link4.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link5.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/link6.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/tracer_base_link.STL filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/tracer_base_link.dae filter=lfs diff=lfs merge=lfs -text
embodiments/aloha-agilex-1/urdf/aloha_maniskill_sim/meshes/tracer_base_link_no_wheel.dae filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/camera_base.stl filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/d435.dae filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/hand.SLDPRT filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/hand.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link0.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link1.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link2.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link3.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link4.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link5.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link6.glb filter=lfs diff=lfs merge=lfs -text
embodiments/franka-panda/franka_description/meshes/visual/link7.glb filter=lfs diff=lfs merge=lfs -text
messy_objects/snack_box/020/textured.obj filter=lfs diff=lfs merge=lfs -text
objects/007_shoe_box/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base7.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base8.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/collision/base9.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base7.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base8.glb filter=lfs diff=lfs merge=lfs -text
objects/041_shoe/visual/base9.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/collision/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/047_mouse/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/collision/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/048_stapler/visual/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/050_bell/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/050_bell/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/050_bell/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/050_bell/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/collision/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/collision/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/collision/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/visual/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/072_electronicscale/visual/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/collision/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/visual/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/077_phone/visual/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/collision/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base1.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base2.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base3.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base4.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base5.glb filter=lfs diff=lfs merge=lfs -text
objects/078_phonestand/visual/base6.glb filter=lfs diff=lfs merge=lfs -text
objects/150_dustbin/collision/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/150_dustbin/visual/base0.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/collision/base13.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/collision/base3822.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/collision/base4064.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/visual/base13.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/visual/base3822.glb filter=lfs diff=lfs merge=lfs -text
objects/151_bottle/visual/base4064.glb filter=lfs diff=lfs merge=lfs -text
