#!/usr/bin/env python3
import sys
sys.path.append('.')

print("开始测试环境创建...")

try:
    from envs.blocks_stack_three import blocks_stack_three
    print("✅ 导入成功")
    
    env = blocks_stack_three()
    print("✅ 环境创建成功")
    
    if hasattr(env, 'headless_mode'):
        print(f"无头模式: {env.headless_mode}")
    
    if hasattr(env, 'close'):
        env.close()
        print("✅ 环境关闭成功")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
