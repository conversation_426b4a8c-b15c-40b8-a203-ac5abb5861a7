{"full_description": "use arms to grab the bottles and put them into the dustbin to the left of the table", "schema": "{A} notify the first bottle, {B} notify the second bottle, {C} notify the third bottle, {D} notify the dustbin", "preference": "num of words should not exceed 15. DegreeOfDetail avg is 6.", "seen": ["Use arms to shift {A} into {D}, then repeat for {B} and {C}.", "Place {A}, {B}, and {C} into {D} one by one.", "Grab {A}, {B}, and {C} sequentially and put them into {D}.", "Pick each of {A}, {B}, and {C} and place them into {D}.", "Use arms to grab {A}, {B}, and {C} and place them into {D}.", "Transfer {A}, {B}, and {C} into {D} using arms.", "Grab {A}, {B}, and {C} and put into {D} to the left.", "Shift {A}, then {B}, and finally {C} into {D}.", "Use arms to move {A}, {B}, and {C} into {D} step by step.", "Move {A}, then {B}, and finally {C} into {D} using arms.", "Use arms to move {A}, {B}, and {C} into {D}.", "Move {A}, {B}, then {C} into the {D} on the left.", "Transfer {A}, {B}, and {C} sequentially into {D}.", "Place {A}, {B}, {C} into {D} using the arms.", "Put {A}, {B}, and {<PERSON>} into {D} on the left.", "Use arms to drop {A}, {B}, and {C} into {D}.", "Grab {A}, {B}, and {C} and place them into {D}.", "Use the arms to transfer {A}, {B}, and {C} to {D}.", "Move {A}, {B}, and {C} into {D} step by step.", "Using the arms, place {A}, {B}, and {C} into {D}.", "Use arms to grab {A} and place it in {D}. Repeat for {B} and {C}.", "Move {A} into {D}, followed by {B} and {C}.", "Transfer {A} to {D}, then transfer {B} and {C} as well.", "Use arms to pick {A}, {B}, and {C}, placing them into {D}.", "Place {A}, {B}, and {C} into {D}, one by one.", "Pick up {A} and drop it into {D}. Repeat for {B} and {C}.", "Grab and move {A}, {B}, and {C} to {D} using the arms.", "First grab {A}, drop it into {D}, then do the same for {B} and {C}.", "Place {A} in {D}, then pick {B} and {C} and place them too.", "Use arms to transfer {A}, {B}, and {C} into {D} sequentially.", "Grab {A}, {B}, {C} and place them into {D}.", "Use arms to place {A}, {B}, {C} into {D}.", "Transfer {A}, {B}, and {C} into {D} step-by-step.", "Transport {A}, {B}, {C} into {D} carefully.", "Pick {A}, {B}, {C} and drop them into {D}.", "Guide {A}, {B}, and {C} one by one into {D}.", "Grab {A}, {B}, and {C} and set them into {D}.", "Place {A}, {B}, and {C} into {D} sequentially.", "Use arms to move {A}, {B}, and {C} to {D}.", "Use arms to pick {A}, {B}, {C}, and drop them into {D}.", "Move {A}, {B}, and {C} into {D} using the arms.", "Grab {A}, {B}, and {C} from the table. Place them in {D}.", "Pick {A}, {B}, and {<PERSON>} and put all three into {D}.", "Use arms to place {A}, {B}, and {C} inside {D}.", "Move {A}, {B}, {C} to {D} using arms.", "Place {A}, {B}, {C} into {D} one by one.", "Lift {A}, {B}, {C} and place them inside {D}.", "Grab and move {A}, {B}, {C} into the dustbin {D}.", "With arms, transfer {A}, {B}, and {C} to {D}."], "unseen": ["Move {A}, {B}, and {C} into {D}.", "Pick {A}, {B}, and {<PERSON>} and drop them into {D}.", "Place {A}, {B}, and {C} inside {D} one by one.", "Grab {A} and put it in {D}. Repeat for {B} and {C}.", "Pick {A}, drop it in {D}, then do the same for {B} and {C}.", "Move {A}, {B}, and {C} to {D} one by one.", "Put {A}, {B}, and {C} into {D} in sequence.", "Grab {A} and put it into {D}. Then repeat for {B} and {C}.", "Place {A}, {B}, and {C} into {D}, one after another."]}