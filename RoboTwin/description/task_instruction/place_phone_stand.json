{"full_description": "pick up the phone and put it on the phone stand", "schema": "{A} notify the phone, {B} notify the phonestand.Arm use literal 'arm'", "preference": "num of words should not exceed 5", "seen": ["Lift {A} and position {B}", "Move {A} to {B} with arm", "Place {A} on {B} carefully", "Take {A}, position it {B}", "Arm moves {A} to {B}", "Hold {A}, align onto {B}", "Pick {A} and drop {B}", "Set {A} onto {B} via arm", "Transfer {A} to {B} with arm", "Place {A} on {B}", "Drop {A} onto {B}", "Put {A} onto {B}", "Lift {A} to {B}", "Stick {A} onto {B}", "Pick up {A} and drop it on {B}", "With arm, grab {A} then position it on {B}", "Lift {A} and place it down on {B}", "With the use of arm, take {A} to {B}", "Take hold of {A} and set it onto {B}", "Use the arm for {A} and align it on {B}", "Lift {A}, move it to {B}", "<PERSON> picks up {A}, places it on {B}", "Retrieve {A} and position it on {B}", "Take {A} to {B} using arm", "Lift {A}, put on {B}", "Grab {A} with arm, place {B}", "Use arm to move {A} to {B}", "Pick {A}, align with {B}", "Set {A} down on {B}", "Use arm, grab {A}, place {B}", "Lift {A} carefully, move to {B}", "Grab {A} using arm, set on {B}", "Pick {A}, position it over {B}", "Move {A} into place on {B}", "Set {A} onto {B}.", "Pick up {A} onto {B}.", "Move {A} to {B}.", "Put down {A} on {B}.", "Place {A} on {B}.", "Drop {A} onto {B}."], "unseen": ["Place {A} on {B}.", "Arm lifts {A} to {B}.", "Set {A} onto {B}.", "{A} goes on {B}.", "Move {A} onto {B}.", "Arm: {A} to {B}.", "Put {A} on {B}.", "Transfer {A} to {B}.", "Mount {A} on {B}.", "Set {A} on {B} now."]}