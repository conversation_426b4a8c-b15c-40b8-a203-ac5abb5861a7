{"full_description": "use both arms to pick up the two shoes on the table and put them in the shoebox, with the shoe tip pointing to the left", "schema": "{A} notify one shoe(use 'two {A}' or 'a pair of {}').{B} nofity the shoebox", "preference": "num of words should not exceed 10.IN EACH INSTRUCTION, YOU MUST STRESS THE SHOE TIP POINTING TO THE LEFT", "seen": ["Ensure tips point left, put {A} in {B}.", "Pick two {A}, place in {B}, tips left.", "With both arms, set {A} in {B}, tips left.", "Drop a pair of {A} into the {B}, tips left.", "Pick {A}, place in {B}, ensure tips point left.", "Set two {A} in {B}, make shoe tips point left.", "Using both arms, position {A} in {B}, tips left.", "Arrange a pair of {A} in {B}, tips must point left.", "With arms, grab {A} and put in {B}, tips left.", "Place two {A} in {B}, ensure shoe tips point left.", "Use both arms to set {A} inside {B} tip left", "Place two {A} in {B}, tips must face left", "Put two {A} into {B} ensuring tips face left", "Set two {A} into {B} making tips point left", "Grab two {A}, put in {B}, and tips left", "Lift two {A}, place in {B} ensuring tips left", "Move two {A} into {B}, tips need to point left", "Pick up two {A}, place in {B}, tips left", "Using both arms, set {A} in {B} tips left", "Put both {A} into {B} with tips placed left", "Put two {A} in {B} oriented tip-left.", "Place the pair of {A} inside {B} with tips pointing left.", "Lift two {A} into {B}, ensuring tips face left.", "Pick up two {A} and orient them tip-left in {B}.", "Move the two {A} into {B} with tips to the left.", "Take two {A} and put them in {B} with the tip left.", "Set the pair of {A} into {B}, ensuring tips face left.", "With both arms, place two {A} in {B}, tips pointing left.", "Arrange the two {A} into {B} and ensure tips point left.", "Handle two {A} into {B}, making sure tips face left.", "Ensure {A} tips face left in the {B}.", "Pick up two {A}, place them tip-left in {B}.", "Place a pair of {A} in {B}, tips left.", "With arms, put two {A} in {B} leftwards.", "Use arms and ensure {A} tips face left in {B}.", "Place {A} in {B} with tips pointing left.", "Ensure {A} tips face left, then set them in {B}.", "Lift both {A} using arms, put tip-left into {B}.", "Put {A} in {B}, ensuring tips face leftwards.", "With arms, place both {A} tip-left into {B}.", "Use arms to move two {A} into {B}, tips left.", "Transfer two {A} to {B}, ensuring tips face left.", "Lift two {A} and position them in {B} tip-left.", "Put two {A} into {B} with tips pointing left.", "Place a pair of {A} in {B} tips-left.", "Set a pair of {A} inside {B}, pointing tips left.", "Pick and lay two {A} in {B}, tips to the left.", "Use your arms to arrange two {A} in {B}, tips left.", "Pick up two {A}, drop into {B}, tips pointing left.", "Lay down a pair of {A} in {B}, ensuring tips left."], "unseen": ["Place two {A} in {B}, tips left.", "Use two arms, put {A} in {B}, tips left.", "Grab two {A} and place them in {B} tip pointing left", "Pick two {A}, put them in {B} with tips left", "Grab two {A} and place them in {B} tip-left.", "Use both arms to set two {A} into {B} with tips left.", "Put two {A} tip-left into {B}.", "Use arms to put {A} in {B}, tip left.", "Grab two {A} and set in {B} tip-left.", "Pick two {A}, place in {B}, tips left."]}