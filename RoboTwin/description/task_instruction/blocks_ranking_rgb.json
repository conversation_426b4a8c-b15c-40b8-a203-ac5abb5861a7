{"full_description": "<Place> the red block, green block, and blue block <in the order> of red, green, and blue from left to right, <placing in a row>.", "schema": "{A} notify the red block, {B} notify the green block, {C} notify the blue block, {a} notify the arm to grab red block, {b} notify the arm to grab green block, {c} notify the arm to grab blue block", "preference": "num of words should not exceed 30", "seen": ["Using {a}, place {A} followed by {B} and {<PERSON>} in a neat row, ordered from left to right.", "Start with {a} to grab {A}, setting it on the left. Then use {b} for {B} next to {A}. Lastly, {c} places {C} to complete the sequence.", "Place the items {A}, {B}, and {C} from left to right, arranging them in order.", "Grab {A} using {a}, set it down to start the row. Afterward, take {B} with {b} and align it next. Use {c} for {C} at the end.", "Arrange them sequentially from left to right: {A}, {B}, then {C}.", "Use {a} to move {A} first, then {b} handles {B} and {C} is placed by {c} to finish the arrangement.", "Order all three blocks by placing {A} on the left-most side using {a}, then move onto {B} with {b}, and end with {C} via {c}.", "Put {A}, {B}, and {<PERSON>}, left to right, in a consecutive row.", "Using arms {a}, {b}, and {c}, sequentially set {A}, {B}, and {C} from left to right in line formation.", "Align {A}, {B}, and {<PERSON>} neatly from left to right in a row.", "Pick up {A} using {a} and place it at the far left. Then grab {B} with {b} and align it next to {A}. Finally, the {c} positions {C} last.", "Place the {A}, {B}, and {C} blocks sequentially, forming a row from leftmost.", "The {A} block goes first with {a}, followed by {B} using {b}, and ends with {C} handled via {c}.", "Using {a}, {b}, and {c}, position each block {A}, {B}, {C} to complete the ordered row from left-to-right.", "Begin by using {a} to pick {A} and set it at the leftmost point. {b} moves {B} to the middle next, finishing with {c} placing {C} at the right.", "Set {A}, {B}, and {<PERSON>} down in sequence from the left to right ends.", "Grab {A} using {a} to begin the row, arrange {B} next using {b}, and complete with {C} towards the right using {c}.", "Line the blocks up from left to right: take {A} with {a} for the first spot, {B} with {b} next, and {C} handled by {c} for the ending spot.", "Position {A}, {B}, and {C} consecutively in a horizontal row left to right.", "With {a}, {b}, and {c}, arrange {A}, {B}, and {C} sequentially to form a left-to-right ordered line.", "Use {a} to place {A}, followed by {b} setting {B}, and lastly {c} with {C} in a row left to right.", "Grab {A} with {a}, {B} with {b}, and {C} with {c}, arranging them in that order.", "Arrange {A}, {B}, and {<PERSON>} from left to right in a row, respecting the sequence.", "With the {a}, position {A}, then switch to {b} for {B}, and finally {c} places {C} in a row.", "Set {A}, {B}, and {<PERSON>} in a row in the left-to-right order of red, green, blue.", "Using {a}, {b}, and {c}, arrange {A}, {B}, and {C} in a row left to right.", "Place all blocks, starting with {A}, progressing to {B}, and then finishing with {C} in a line.", "Put {A} on the left, then {B} with {b} in the middle, and {C} with {c} on the right.", "Line up {A}, {B}, and {C} so that red is first, green second, and blue at the end.", "Arrange {A}, {B}, and {C} using {a}, {b}, and {c} from left to right.", "Using {a}, pick up {A} and place it first, followed by {b} picking up {B}, and finally {c} placing {C} together in a left-to-right row.", "Pick each block starting with {A}, followed by {B}, and finally {C}, arranging them left to right in a line.", "Grab {A} with {a}, {B} with {b}, and {C} with {c}, then line them up from the left in given sequence.", "Place {A}, {B}, and {C} in left-to-right order, starting with red, followed by green, and finishing with blue.", "Using {a}, pick {A}, then {b} for {B}, and lastly {c} for {C}, setting the blocks from the left in order.", "Arrange {A}, {B}, and {C} in a neat row from left to right, starting with red, followed by green and blue.", "Grab {A} with {a}, {B} with {b}, and {C} with {c}, and arrange them in a row in their respective order.", "Organize blocks {A}, {B}, and {C} sequentially from left to right, maintaining the red, green, blue order.", "Use {a}, {b}, and {c} to retrieve and place {A}, {B}, and {C} from left to right in order.", "Line up {A}, {B}, and {C} in sequence from left going to right, starting with the red and ending with blue.", "Use the {a} to place {A} at the left, followed by {b} placing {B} next, and finally use the {c} for {C} on the right.", "Arrange {A}, {B}, and {C} in a left-to-right order using {a} for {A}, {b} for {B}, and {c} for {C}.", "Start by setting {A} to the leftmost spot, follow it with {B}, then {C} at the far-right.", "Take the {a} to position {A} on the far left, promptly use {b} to set {B}, and finish by having {c} place {C} to the rightmost point.", "Place {A} to the leftmost side, then {B} in the middle, followed by {C} at the right in a row.", "Grab {A}, {B}, and {C} using respective arms, {a}, {b}, {c}, and arrange them left to right in the stated order.", "Set {A}, {B}, and {C} sequentially in a horizontal row from left to right as red, green, and then blue.", "Using {a} for {A}, {b} for {B}, and {c} for {C}, create a left-to-right row as instructed.", "Position {A}, {B}, {C} in a row, with {A} on the left, {B} next, and {C} at the far right.", "Pick up and place {A} using the {a} on the left, {B} using {b} next, then {C} with {c} on the right."], "unseen": ["Use {a} to set {A} on the left, then {b} to place {B} in the middle, and finally {c} to add {C} on the right, forming a row.", "Arrange {A}, {B}, and {<PERSON>} in a left-to-right row so that red comes first, followed by green, then blue.", "Line up {A}, {B}, and {<PERSON>} in a row, starting with {A} on the left, {B} in the center, and {<PERSON>} on the right.", "Direct {a} to put down {A} on the left, next {b} sets {B} beside it, and {c} finishes by adding {C} to create a row.", "Start with {A} on the left, add {B} next, then finish with {C} to form a straight row in that order.", "Guide {a} to lay down {A} first, then have {b} follow with {B}, and let {c} finish by lining up {C} to the right.", "Place {A}, {B}, and {<PERSON>} in a single line, ensuring {A} is first on the left, then {B}, and {<PERSON>} last on the right.", "Handle {A} using {a} for the far left, position {B} by {b} beside it, and have {c} add {C} at the end of the row.", "In a row, start with {A} on the left, then {B} in the center, and finally {C} on the right side.", "Let {a} move {A} to the leftmost spot, followed by {b} arranging {B}, and finally {c} placing {C} to the right.", "Set up {A}, {B}, and {<PERSON>} in a row so that {A} sits leftmost, {B} is in the middle, and {C} ends up on the right."]}