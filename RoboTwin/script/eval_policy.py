import sys
import subprocess
sys.path.append('./') 
sys.path.append(f'./policy')
sys.path.append('./description/utils') 
from envs import CONFIGS_PATH

import os
import numpy as np
from pathlib import Path
from collections import deque
import traceback

import yaml
from datetime import datetime
import importlib
import argparse
import pdb

from generate_episode_instructions import *

current_file_path = os.path.abspath(__file__)
parent_directory = os.path.dirname(current_file_path)

def class_decorator(task_name):
    envs_module = importlib.import_module(f'envs.{task_name}')
    try:
        env_class = getattr(envs_module, task_name)
        env_instance = env_class()
    except:
        raise SystemExit("No Task")
    return env_instance

def eval_function_decorator(policy_name, model_name):
    try:
        policy_model = importlib.import_module(policy_name)
        func = getattr(policy_model, model_name)
        return func
    except ImportError as e:
        raise e

def get_camera_config(camera_type):
    camera_config_path = os.path.join(parent_directory, '../task_config/_camera_config.yml')

    assert os.path.isfile(camera_config_path), "task config file is missing"

    with open(camera_config_path, 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    assert camera_type in args, f'camera {camera_type} is not defined'
    return args[camera_type]

def get_embodiment_config(robot_file):
    robot_config_file = os.path.join(robot_file, 'config.yml')
    with open(robot_config_file, 'r', encoding='utf-8') as f:
        embodiment_args = yaml.load(f.read(), Loader=yaml.FullLoader)
    return embodiment_args

def main(usr_args):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    task_name = usr_args['task_name']
    setting = usr_args['setting']
    # checkpoint_num = usr_args['checkpoint_num']
    policy_name = usr_args['policy_name']
    instruction_type = usr_args['instruction_type']
    save_dir = None
    video_save_dir = None
    video_size = None

    get_model = eval_function_decorator(policy_name, 'get_model')
    # from policy.ACT.deploy_policy import get_model

    with open(f'./task_config/{task_name}.yml', 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    args['setting'] = setting
    
    embodiment_type = args.get('embodiment')
    embodiment_config_path = os.path.join(CONFIGS_PATH, '_embodiment_config.yml')

    with open(embodiment_config_path, 'r', encoding='utf-8') as f:
        _embodiment_types = yaml.load(f.read(), Loader=yaml.FullLoader)
    
    def get_embodiment_file(embodiment_type):
        robot_file = _embodiment_types[embodiment_type]['file_path']
        if robot_file is None:
            raise "No embodiment files"
        return robot_file

    with open(CONFIGS_PATH + '_camera_config.yml', 'r', encoding='utf-8') as f:
        _camera_config = yaml.load(f.read(), Loader=yaml.FullLoader)
    
    head_camera_type = args['camera']['head_camera_type']
    args['head_camera_h'] = _camera_config[head_camera_type]['h']
    args['head_camera_w'] = _camera_config[head_camera_type]['w']
    
    if len(embodiment_type) == 1:
        args['left_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['right_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['dual_arm_embodied'] = True
    elif len(embodiment_type) == 3:
        args['left_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['right_robot_file'] = get_embodiment_file(embodiment_type[1])
        args['embodiment_dis'] = embodiment_type[2]
        args['dual_arm_embodied'] = False
    else:
        raise "embodiment items should be 1 or 3"
    
    args['left_embodiment_config'] = get_embodiment_config(args['left_robot_file'])
    args['right_embodiment_config'] = get_embodiment_config(args['right_robot_file'])
    
    if len(embodiment_type) == 1:
        embodiment_name = str(embodiment_type[0])
    else:
        embodiment_name = str(embodiment_type[0]) + '+' + str(embodiment_type[1])

    save_dir_folder_name = str(args['task_name']) + '/' + f'{policy_name}' + '/' + f'{embodiment_name}' + '-m' + str(int(args['augmentation']['messy_table'])) + '_b' + str(int(args['augmentation']['random_background'])) \
                         + '_l' + str(int(args['augmentation']['random_background'])) + '_h' + str(args['augmentation']['random_table_height']) + '_c' + str(args['augmentation']['random_head_camera_dis']) + '_' + str(args['camera']['head_camera_type'])
    
    save_dir = Path(f'eval_result/{save_dir_folder_name}/{setting}/{current_time}')
    save_dir.mkdir(parents=True, exist_ok=True)

    if args['eval_video_log']:
        video_save_dir = save_dir
        camera_config = get_camera_config(args['camera']['head_camera_type'])
        video_size = str(camera_config['w']) + 'x' + str(camera_config['h'])
        video_save_dir.mkdir(parents=True, exist_ok=True)
        args['eval_video_save_dir'] = video_save_dir

    # output camera config
    print('============= Config =============\n')
    print('\033[95mMessy Table:\033[0m ' + str(args['augmentation']['messy_table']))
    print('\033[95mRandom Background:\033[0m ' + str(args['augmentation']['random_background']))
    if args['augmentation']['random_background']:
        print(' - Clean Background Rate: ' + str(args['augmentation']['clean_background_rate']))
    print('\033[95mRandom Light:\033[0m ' + str(args['augmentation']['random_light']))
    if args['augmentation']['random_light']:
        print(' - Crazy Random Light Rate: ' + str(args['augmentation']['crazy_random_light_rate']))
    print('\033[95mRandom Table Height:\033[0m ' + str(args['augmentation']['random_table_height']))
    print('\033[95mRandom Head Camera Distance:\033[0m ' + str(args['augmentation']['random_head_camera_dis']))

    print('\033[94mHead Camera Config:\033[0m '+ str(args['camera']['head_camera_type']) + f', ' + str(args['camera']['collect_head_camera']))
    print('\033[94mWrist Camera Config:\033[0m '+ str(args['camera']['wrist_camera_type']) + f', ' + str(args['camera']['collect_wrist_camera']))
    print('\033[94mEmbodiment Config:\033[0m '+ embodiment_name)  
    print('\n==================================')

    TASK_ENV = class_decorator(args['task_name'])
    args['policy_name'] = policy_name
    usr_args['left_arm_dim'] = len(args['left_embodiment_config']['arm_joints_name'][0])
    usr_args['right_arm_dim'] = len(args['right_embodiment_config']['arm_joints_name'][1])
    
    seed = usr_args['seed']

    st_seed = 100000 * (1+seed)
    suc_nums = []
    test_num = 50 
    topk = 1

    model = get_model(usr_args)
    st_seed, suc_num = eval_policy(task_name, TASK_ENV, args, model, st_seed, test_num=test_num, video_size=video_size, instruction_type=instruction_type)
    suc_nums.append(suc_num)

    topk_success_rate = sorted(suc_nums, reverse=True)[:topk]

    file_path = os.path.join(save_dir, f'_result.txt')
    with open(file_path, 'w') as file:
        file.write(f'Timestamp: {current_time}\n\n')
        file.write(f'Instruction Type: {instruction_type}\n\n')
        # file.write(str(task_reward) + '\n')
        file.write('\n'.join(map(str, np.array(suc_nums) / test_num)))

    print(f'Data has been saved to {file_path}')
    # return task_reward
    

def eval_policy(task_name, TASK_ENV, args, model, st_seed, test_num=100, video_size=None, instruction_type=None):
    print(f"\033[34mTask Name: {args['task_name']}\033[0m")
    print(f"\033[34mPolicy Name: {args['policy_name']}\033[0m")

    expert_check = True
    TASK_ENV.suc = 0
    TASK_ENV.test_num = 0

    now_id = 0
    succ_seed = 0
    suc_test_seed_list = []

    policy_name = args['policy_name']
    eval_func = eval_function_decorator(policy_name, 'eval')
    reset_func = eval_function_decorator(policy_name, 'reset_model')
    
    now_seed = st_seed
    task_total_reward = 0

    while succ_seed < test_num:
        render_freq = args['render_freq']
        args['render_freq'] = 0
        
        if expert_check:
            try:
                TASK_ENV.setup_demo(now_ep_num=now_id, seed=now_seed, is_test=True, ** args)
                episode_info = TASK_ENV.play_once()
                TASK_ENV.close()
            except Exception as e:
                stack_trace = traceback.format_exc()
                print(' -------------')
                print('Error: ', stack_trace)
                print(' -------------')
                TASK_ENV.close()
                now_seed += 1
                args['render_freq'] = render_freq
                print('error occurs !')
                continue

        if (not expert_check) or ( TASK_ENV.plan_success and TASK_ENV.check_success() ):
            succ_seed += 1
            suc_test_seed_list.append(now_seed)
        else:
            now_seed += 1
            args['render_freq'] = render_freq
            continue

        args['render_freq'] = render_freq
        args['eval_mode'] = True

        TASK_ENV.setup_demo(now_ep_num=now_id, seed=now_seed, is_test=True, **args)
        episode_info_list = [episode_info['info']]
        results = generate_episode_descriptions(args['task_name'], episode_info_list, test_num)
        instruction = np.random.choice(results[0][instruction_type])
        TASK_ENV.set_instruction(instruction=instruction) # set language instruction

        if TASK_ENV.eval_video_path is not None:
            ffmpeg = subprocess.Popen([
                'ffmpeg', '-y', '-loglevel', 'error',
                '-f', 'rawvideo',
                '-pixel_format', 'rgb24',
                '-video_size', video_size,
                '-framerate', '10',
                '-i', '-',
                '-pix_fmt', 'yuv420p',
                '-vcodec', 'libx264',
                '-crf', '23',
                f'{TASK_ENV.eval_video_path}/episode{TASK_ENV.test_num}.mp4'
            ], stdin=subprocess.PIPE)
            TASK_ENV._set_eval_video_ffmpeg(ffmpeg)

        # TASK_ENV.apply_policy_demo(policy)
        succ = False
        reset_func(model)
        while TASK_ENV.take_action_cnt < TASK_ENV.step_lim:
            observation = TASK_ENV.get_obs()
            eval_func(TASK_ENV, model, observation)
            if TASK_ENV.eval_success:
                succ = True
                break
        # task_total_reward += TASK_ENV.episode_score
        TASK_ENV._del_eval_video_ffmpeg()

        if succ:
            TASK_ENV.suc += 1
            print("\033[92mSuccess!\033[0m")
        else:
            print("\033[91mFail!\033[0m")

        now_id += 1
        TASK_ENV.close()

        if TASK_ENV.render_freq:
            TASK_ENV.viewer.close()
        
        TASK_ENV.test_num += 1

        print(f"[ {task_name} | {args['policy_name']} | {args['setting']} ]\nSuccess rate:{TASK_ENV.suc}/{TASK_ENV.test_num} => {round(TASK_ENV.suc/TASK_ENV.test_num*100, 1)}%, current seed: {now_seed}\n")
        # TASK_ENV._take_picture()
        now_seed += 1

    return now_seed, TASK_ENV.suc #, task_total_reward

def parse_args_and_config():
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, required=True)
    parser.add_argument('--overrides', nargs=argparse.REMAINDER)
    args = parser.parse_args()

    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # Parse overrides
    def parse_override_pairs(pairs):
        override_dict = {}
        for i in range(0, len(pairs), 2):
            key = pairs[i].lstrip('--')
            value = pairs[i+1]
            try:
                value = eval(value)
            except:
                pass
            override_dict[key] = value
        return override_dict

    if args.overrides:
        overrides = parse_override_pairs(args.overrides)
        config.update(overrides)

    return config

if __name__ == "__main__":
    from test_render import Sapien_TEST
    Sapien_TEST()

    usr_args = parse_args_and_config()    

    main(usr_args)
