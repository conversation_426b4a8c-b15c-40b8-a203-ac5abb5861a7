/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/_vulkan_tricks.py:37: UserWarning: Failed to find Vulkan ICD file. This is probably due to an incorrect or partial installation of the NVIDIA driver. SAPIEN will attempt to provide an ICD file anyway but it may not work.
  warn(
🚀 开始基础功能测试...
[Planner.py]: Curobo not installed! No module named 'curobo'

🧪 测试 blocks_stack_three...
  ✅ 环境类 blocks_stack_three 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 blocks_stack_three 测试通过!

🧪 测试 dual_shoes_place...
  ✅ 环境类 dual_shoes_place 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 dual_shoes_place 测试通过!

🧪 测试 place_object_scale...
  ✅ 环境类 place_object_scale 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 place_object_scale 测试通过!

🧪 测试 place_phone_stand...
  ✅ 环境类 place_phone_stand 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 place_phone_stand 测试通过!

🧪 测试 put_bottles_dustbin...
  ✅ 环境类 put_bottles_dustbin 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 put_bottles_dustbin 测试通过!

🧪 测试 blocks_ranking_rgb...
  ✅ 环境类 blocks_ranking_rgb 导入成功
  ✅ 环境实例创建成功
  ✅ 具有 reset 方法
  ✅ 具有 step 方法
  ✅ 具有 close 方法
  🎉 blocks_ranking_rgb 测试通过!

==================================================
📊 测试结果总结:
==================================================
  blocks_stack_three   ✅ 通过
  dual_shoes_place     ✅ 通过
  place_object_scale   ✅ 通过
  place_phone_stand    ✅ 通过
  put_bottles_dustbin  ✅ 通过
  blocks_ranking_rgb   ✅ 通过

总计: 6/6 个任务测试通过
🎉 所有测试都通过了！
