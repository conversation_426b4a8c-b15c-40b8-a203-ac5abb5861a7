#!/usr/bin/env python3
import sys
sys.path.append('.')

print("基础场景测试开始...")

try:
    import sapien.core as sapien
    print("✅ SAPIEN导入成功")
    
    # 尝试创建引擎
    engine = sapien.Engine()
    print("✅ 引擎创建成功")
    
    # 尝试创建场景配置
    scene_config = sapien.SceneConfig()
    print("✅ 场景配置创建成功")
    
    # 尝试创建场景
    scene = engine.create_scene(scene_config)
    print("✅ 场景创建成功")
    
    print("✅ 基础场景测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
