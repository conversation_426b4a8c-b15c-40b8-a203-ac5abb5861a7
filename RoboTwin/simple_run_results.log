/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/_vulkan_tricks.py:37: UserWarning: Failed to find Vulkan ICD file. This is probably due to an incorrect or partial installation of the NVIDIA driver. SAPIEN will attempt to provide an ICD file anyway but it may not work.
  warn(
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/blocks_stack_three.py", line 9, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/blocks_stack_three.py", line 9, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/dual_shoes_place.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/dual_shoes_place.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/place_object_scale.py", line 11, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/place_object_scale.py", line 11, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/place_phone_stand.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/place_phone_stand.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/put_bottles_dustbin.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/put_bottles_dustbin.py", line 8, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/blocks_ranking_rgb.py", line 10, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
Traceback (most recent call last):
  File "/root/autodl-tmp/RoboTwin/simple_run_task.py", line 80, in run_task_simple
    env.setup_demo(now_ep_num=episode, seed=episode + 42, **args)
  File "/root/autodl-tmp/RoboTwin/envs/blocks_ranking_rgb.py", line 10, in setup_demo
    super()._init(**kwags)
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 79, in _init
    self.setup_scene()
  File "/root/autodl-tmp/RoboTwin/envs/_base_task.py", line 145, in setup_scene
    self.renderer = sapien.SapienRenderer()
  File "/root/miniconda3/envs/RoboTwin_Challenge/lib/python3.10/site-packages/sapien/wrapper/renderer.py", line 9, in __init__
    super().__init__()
RuntimeError: failed to find a rendering device
🎯 简化任务运行测试

🚀 开始运行 blocks_stack_three 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
[Planner.py]: Curobo not installed! No module named 'curobo'
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/blocks_stack_three

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 blocks_stack_three 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

🚀 开始运行 dual_shoes_place 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/dual_shoes_place

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 dual_shoes_place 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

🚀 开始运行 place_object_scale 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/place_object_scale

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 place_object_scale 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

🚀 开始运行 place_phone_stand 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/place_phone_stand

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 place_phone_stand 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

🚀 开始运行 put_bottles_dustbin 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/put_bottles_dustbin

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 put_bottles_dustbin 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

🚀 开始运行 blocks_ranking_rgb 任务...
📊 计划运行 2 个episode
✅ 配置文件读取成功
✅ 环境类导入成功
✅ 创建保存目录: ./simple_test_data/blocks_ranking_rgb

📝 运行 Episode 1/2...
  ❌ Episode 1 异常: failed to find a rendering device

📝 运行 Episode 2/2...
  ❌ Episode 2 异常: failed to find a rendering device

📊 blocks_ranking_rgb 任务运行完成:
  ✅ 成功: 0/2
  ❌ 失败: 2/2
  📈 成功率: 0.0%

============================================================
🏆 所有任务运行结果总结:
============================================================
  blocks_stack_three        ❌ 失败
  dual_shoes_place          ❌ 失败
  place_object_scale        ❌ 失败
  place_phone_stand         ❌ 失败
  put_bottles_dustbin       ❌ 失败
  blocks_ranking_rgb        ❌ 失败

总计: 0/6 个任务运行成功
