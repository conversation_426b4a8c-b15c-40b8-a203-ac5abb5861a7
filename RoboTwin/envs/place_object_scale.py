from copy import deepcopy
from ._base_task import Base_Task
from .utils import *
import sapien
import math
import glob
import numpy as np

class place_object_scale(Base_Task):
    def setup_demo(self,**kwags):
        super()._init(**kwags)
        self.create_table_and_wall()
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        self.load_actors()
        if self.messy_table:
            self.get_messy_table()
        self.step_lim = 600
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):
        rand_pos = rand_pose(
            xlim=[-0.25,0.25],
            ylim=[-0.2, 0.1],
            zlim=[0.75+self.bias],
            qpos=[0.5,0.5,0.5,0.5],
            rotate_rand=True,
            rotate_lim=[0,3.14,0]
        )
        while (abs(rand_pos.p[0])<0.02):
            rand_pos = rand_pose(
                xlim=[-0.25,0.25],
                ylim=[-0.2, 0.1],
                zlim=[0.75+self.bias],
                qpos=[0.5,0.5,0.5,0.5],
                rotate_rand=True,
                rotate_lim=[0,3.14,0]
            )    

        def get_available_model_ids(modelname):
            asset_path = os.path.join("assets/objects", modelname)
            json_files = glob.glob(os.path.join(asset_path, "model_data*.json"))
            
            available_ids = []
            for file in json_files:
                base = os.path.basename(file)
                try:
                    idx = int(base.replace("model_data", "").replace(".json", ""))
                    available_ids.append(idx)
                except ValueError:
                    continue
            
            return available_ids

        object_list = ["047_mouse", "048_stapler", "050_bell"] 

        self.selected_modelname = np.random.choice(object_list)

        available_model_ids = get_available_model_ids(self.selected_modelname)
        if not available_model_ids:
            raise ValueError(f"No available model_data.json files found for {self.selected_modelname}")

        self.selected_model_id = np.random.choice(available_model_ids)

        self.object, self.object_data = create_actor(
            scene=self.scene,
            pose=rand_pos,
            modelname=self.selected_modelname,
            convex=True,
            model_id=self.selected_model_id,
        )
        self.object.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.05
        
        if rand_pos.p[0] > 0:
            xlim = [0.02,0.25]
        else:
            xlim = [-0.25,-0.02]
        target_rand_pose = rand_pose(
            xlim=xlim,
            ylim=[-0.2, 0.1],
            zlim=[0.74+self.bias],
            qpos=[0.5,0.5,0.5,0.5],
            rotate_rand=True,
            rotate_lim=[0,3.14,0]
        )
        while (np.sqrt((target_rand_pose.p[0]-rand_pos.p[0])**2+(target_rand_pose.p[1]-rand_pos.p[1])**2) < 0.15):
            target_rand_pose = rand_pose(
                xlim=xlim,
                ylim=[-0.2, 0.1],
                zlim=[0.74+self.bias],
                qpos=[0.5,0.5,0.5,0.5],
                rotate_rand=True,
                rotate_lim=[0,3.14,0]
            )

        self.scale_id = np.random.choice([0,1,5,6],1)[0]

        self.scale, self.scale_data = create_actor(
            self.scene,
            pose=target_rand_pose,
            modelname="072_electronicscale",
            model_id=np.random.choice([0,1,5,6],1)[0],
            convex=True
        )
        self.scale.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.05

        self.add_prohibit_area(self.object, self.object_data, padding=0.05)
        self.add_prohibit_area(self.scale, self.scale_data)

    def play_once(self):
       
        if self.object.get_pose().p[0] > 0:
            self.arm_tag='right'
            move_func = self.right_move_to_pose
        else:
            self.arm_tag='left'
            move_func = self.left_move_to_pose
            
        grasp_pose=self._test_grasp_single_actor(actor=self.object, actor_data=self.object_data, arm_tag = self.arm_tag)
        grasp_pose[2]+=0.15
        move_func(grasp_pose)
        place_pose = self._test_place(self.object, self.object_data, target_pose=self.get_actor_functional_pose(self.scale, self.scale_data, 0), arm_tag=self.arm_tag, constrain='free', pre_dis=0.05, dis=0.005)

        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        info['info'] = {'{A}': f'072_electronicscale/base{self.scale_id}', '{B}': f'{self.selected_modelname}/base{self.selected_model_id}', '{a}': self.arm_tag} 
        return info

    def check_success(self):
        object_pose = self.object.get_pose().p
        scale_pose = self.get_actor_functional_pose(self.scale, self.scale_data, actor_functional_point_id=0)
        distance_threshold = 0.035
        distance = np.linalg.norm(np.array(scale_pose[:2]) - np.array(object_pose[:2]))
        check_arm = self.is_left_gripper_open if self.arm_tag == "left" else self.is_right_gripper_open
        return distance < distance_threshold and object_pose[2] > (scale_pose[2]-0.01) and check_arm()