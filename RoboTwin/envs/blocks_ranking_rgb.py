from ._base_task import Base_Task
from .utils import *
import sapien
import math
import numpy as np

class blocks_ranking_rgb(Base_Task):

    def setup_demo(self,**kwags):
        super()._init(**kwags)
        self.create_table_and_wall()
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        self.load_actors()
        if self.messy_table:
            self.get_messy_table()
        self.step_lim = 850
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):

        def create_block_data(half_size):
            contact_discription_list = []
            contact_points_list = [[[0, 0, 1, 0], [1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 0, 1]]] 
            functional_matrix = np.eye(4)
            functional_matrix[:3,:3] = t3d.euler.euler2mat(np.pi,0,0)
            functional_matrix[:3,3] = np.array([0,0,-half_size[2]])

            data = {
                'center': [0,0,0],
                'extents': half_size,
                'scale': [1,1,1],                                    
                'target_pose': [[[1,0,0,0],[0,1,0,0],[0,0,1,half_size[2]],[0,0,0,1]]],   
                'contact_points_pose' : contact_points_list,  
                'transform_matrix': np.eye(4).tolist(),           
                "functional_matrix": [functional_matrix.tolist()],        
                'contact_points_discription': contact_discription_list,    
                'contact_points_group': [[0, 1, 2, 3]],
                'contact_points_mask': [True],
                'target_point_discription': ["The top surface center of the block." ],
                'functional_point_discription': ["Point0: The center point on the bottom of the block, and functional axis is vertical bottom side down"]
            }

            return data

        while True:
            block_pose_lst = []
            for i in range(3):
                block_pose = rand_pose(
                    xlim=[-0.28,0.28],
                    ylim=[-0.08,0.05],
                    zlim=[0.765+self.bias],
                    qpos=[1,0,0,0],
                    ylim_prop=True,
                    rotate_rand=True,
                    rotate_lim=[0,0,1.],
                )
                def check_block_pose(block_pose):
                    for j in range(len(block_pose_lst)):
                        if np.sum(pow(block_pose.p[:2] - block_pose_lst[j].p[:2],2)) < 0.01:
                            return False
                    return True
                
                while abs(block_pose.p[0]) < 0.05 or np.sum(pow(block_pose.p[:2] - np.array([0,-0.1]),2)) < 0.01 or not check_block_pose(block_pose):
                    block_pose = rand_pose(
                        xlim=[-0.28,0.28],
                        ylim=[-0.08,0.05],
                        zlim=[0.765+self.bias],
                        qpos=[1,0,0,0],
                        ylim_prop=True,
                        rotate_rand=True,
                        rotate_lim=[0,0,1.],
                    )
                block_pose_lst.append(deepcopy(block_pose))
            eps = [0.10,0.03,0.015]
            block1_pose = block_pose_lst[0].p
            block2_pose = block_pose_lst[1].p
            block3_pose = block_pose_lst[2].p
            if np.all(abs(block1_pose[:2] - block2_pose[:2]) < eps[:2]) and \
                np.all(abs(block2_pose[:2] - block3_pose[:2]) < eps[:2]) and \
                block1_pose[0] < block2_pose[0] and block2_pose[0] < block3_pose[0]:
                continue
            else:
                break

        size = np.random.uniform(0.015,0.025)
        half_size = (size, size, size)
        self.block1 = create_box(
            scene = self.scene,
            pose = block_pose_lst[0],
            half_size=half_size,
            color=(1,0,0),
            name="box"
        )
        self.block2 = create_box(
            scene = self.scene,
            pose = block_pose_lst[1],
            half_size=half_size,
            color=(0,1,0),
            name="box"
        )
        self.block3 = create_box(
            scene = self.scene,
            pose = block_pose_lst[2],
            half_size=half_size,
            color=(0,0,1),
            name="box"
        )

        self.block1_data = self.block2_data = self.block3_data = create_block_data([0.025,0.025,0.025])
        
        self.block1.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01
        self.block2.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01
        self.block3.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01

        pose = self.block1.get_pose().p
        self.prohibited_area.append([pose[0]-0.02,pose[1]-0.02,pose[0]+0.02,pose[1]+0.02])
        pose = self.block2.get_pose().p
        self.prohibited_area.append([pose[0]-0.02,pose[1]-0.02,pose[0]+0.02,pose[1]+0.02]) 
        pose = self.block3.get_pose().p
        self.prohibited_area.append([pose[0]-0.02,pose[1]-0.02,pose[0]+0.02,pose[1]+0.02])

        self.prohibited_area.append([-0.17, -0.22, 0.17, -0.12])

    def play_once(self):
        
        self.las_gripper = None

        y_pose = np.random.uniform(-0.2,-0.1)

        block1_target_pose = [np.random.uniform(-0.08,-0.07), y_pose, 0.74+self.bias]
        block2_target_pose = [np.random.uniform(-0.01,0.01), y_pose, 0.74+self.bias]
        block3_target_pose = [np.random.uniform(0.07,0.08), y_pose, 0.74+self.bias]

        self.block1_target_pose = block1_target_pose
        self.block2_target_pose = block2_target_pose
        self.block3_target_pose = block3_target_pose

        block1_pose = self.block1.get_pose().p
        block3_pose = self.block3.get_pose().p

        arm_tag1, arm_tag2, arm_tag3 = None, None, None

        if block1_pose[0]<0 and block3_pose[0]>0:
            arm_tag1, arm_tag3 = self.together_pick_and_place_two_blocks(self.block1, self.block1_data, block1_target_pose, self.block3, self.block3_data, block3_target_pose)
            arm_tag2 = self.pick_and_place_block(self.block2, self.block2_data, block2_target_pose, self.block2_data)
        else:
            arm_tag1 = self.pick_and_place_block(self.block1, self.block1_data, block1_target_pose)
            arm_tag2 = self.pick_and_place_block(self.block2, self.block2_data, block2_target_pose, self.block1_data)
            arm_tag3 = self.pick_and_place_block(self.block3, self.block3_data, block3_target_pose, self.block2_data)
    
        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        info['info'] = {'{A}': "red block", '{B}': "green block", '{C}': "blue block", '{a}': arm_tag1, '{b}': arm_tag2, '{c}': arm_tag3}
        return info
        
    def pick_and_place_block(self, block, block_data, target_block, target_block_data = None):
        block_pose = block.get_pose().p
        arm_tag = 'left' if block_pose[0] < 0 else 'right'
        pre_grasp_pose = self.get_grasp_pose_w_labeled_direction(block, block_data, pre_dis=0.09, contact_point_id=0, arm_tag=arm_tag)
        target_grasp_pose = self.get_grasp_pose_w_labeled_direction(block, block_data, pre_dis=0.02, contact_point_id=0, arm_tag=arm_tag)

        move_func = self.left_move_to_pose if arm_tag == 'left' else self.right_move_to_pose
        close_gripper = self.close_left_gripper if arm_tag == 'left' else self.close_right_gripper
        open_gripper = self.open_left_gripper if arm_tag == 'left' else self.open_right_gripper
        
        if self.las_gripper is not None and (self.las_gripper != arm_tag and self.las_gripper != 'dual'):
            left_target_pose = self.robot.left_original_pose if self.las_gripper == 'left' else pre_grasp_pose
            right_target_pose = self.robot.right_original_pose if self.las_gripper == 'right' else pre_grasp_pose
            self.together_move_to_pose(left_target_pose=left_target_pose, right_target_pose=right_target_pose)
        elif self.las_gripper == 'dual':
            left_target_pose = self.robot.left_original_pose 
            right_target_pose = self.robot.right_original_pose 
            self.together_move_to_pose(left_target_pose=pre_grasp_pose, right_target_pose=right_target_pose) if arm_tag == 'left' else \
                self.together_move_to_pose(left_target_pose=left_target_pose, right_target_pose=pre_grasp_pose)        
        else:
            move_func(pre_grasp_pose)

        move_func(target_grasp_pose)
        close_gripper()
        move_func(pre_grasp_pose)
        
        target_pose = self.get_actor_goal_pose(target_block, target_block_data, 0)
        target_approach_direction = [0,1,0,0]
        pre_place_pose = self.get_grasp_pose_from_goal_point_and_direction(block, block_data, endpose_tag=arm_tag, actor_functional_point_id=0, target_point=target_pose, target_approach_direction=target_approach_direction, pre_dis=0.09)
        target_place_pose = self.get_grasp_pose_from_goal_point_and_direction(block, block_data, endpose_tag=arm_tag, actor_functional_point_id=0, target_point=target_pose, target_approach_direction=target_approach_direction, pre_dis=0.02)
        
        pre_place_pose[-3:] = [0,0.707,0,-0.707] if arm_tag == 'right' else [-0.707,0,-0.707,0]
        target_place_pose[-3:] = [0,0.707,0,-0.707] if arm_tag == 'right' else [-0.707,0,-0.707,0]
        move_func(pre_place_pose)
        move_func(target_place_pose)
        open_gripper()
        move_func(pre_place_pose)
        self.las_gripper = arm_tag
        return arm_tag

    def together_pick_and_place_two_blocks(self, block1, block1_data, block1_target_pose, block3, block3_data, block3_target_pose):
        block1_pose = block1.get_pose().p
        arm_tag1 = 'left' if block1_pose[0] < 0 else 'right'
        pre_grasp_pose1 = self.get_grasp_pose_w_labeled_direction(block1, block1_data, pre_dis=0.09, contact_point_id=0, arm_tag=arm_tag1)
        target_grasp_pose1 = self.get_grasp_pose_w_labeled_direction(block1, block1_data, pre_dis=0.02, contact_point_id=0, arm_tag=arm_tag1)

        block3_pose = block3.get_pose().p
        arm_tag2 = 'left' if block3_pose[0] < 0 else 'right'
        pre_grasp_pose2 = self.get_grasp_pose_w_labeled_direction(block3, block3_data, pre_dis=0.09, contact_point_id=0, arm_tag=arm_tag2)
        target_grasp_pose2 = self.get_grasp_pose_w_labeled_direction(block3, block3_data, pre_dis=0.02, contact_point_id=0, arm_tag=arm_tag2)

        move_together_func = self.together_move_to_pose
        close_gripper_together = self.together_close_gripper
        open_gripper_together = self.together_open_gripper
        
        move_together_func(pre_grasp_pose1, pre_grasp_pose2)
        move_together_func(target_grasp_pose1, target_grasp_pose2)
        close_gripper_together()
        move_together_func(pre_grasp_pose1, pre_grasp_pose2)
        
        target_pose1 = self.get_actor_goal_pose(block1_target_pose, block1_data, 0)
        target_pose3 = self.get_actor_goal_pose(block3_target_pose, block3_data, 0)

        target_approach_direction = [0,1,0,0]
        pre_place_pose1 = self.get_grasp_pose_from_goal_point_and_direction(block1, block1_data, endpose_tag=arm_tag1, actor_functional_point_id=0, target_point=target_pose1, target_approach_direction=target_approach_direction, pre_dis=0.09)
        target_place_pose1 = self.get_grasp_pose_from_goal_point_and_direction(block1, block1_data, endpose_tag=arm_tag1, actor_functional_point_id=0, target_point=target_pose1, target_approach_direction=target_approach_direction, pre_dis=0.02)

        pre_place_pose2 = self.get_grasp_pose_from_goal_point_and_direction(block3, block3_data, endpose_tag=arm_tag2, actor_functional_point_id=0, target_point=target_pose3, target_approach_direction=target_approach_direction, pre_dis=0.09)
        target_place_pose2 = self.get_grasp_pose_from_goal_point_and_direction(block3, block3_data, endpose_tag=arm_tag2, actor_functional_point_id=0, target_point=target_pose3, target_approach_direction=target_approach_direction, pre_dis=0.02)

        pre_place_pose1[-3:] = [0,0.707,0,-0.707] 
        target_place_pose1[-3:] = [0,0.707,0,-0.707] 
        pre_place_pose2[-3:] = [-0.707,0,-0.707,0]
        target_place_pose2[-3:] = [-0.707,0,-0.707,0]

        move_together_func(pre_place_pose1, pre_place_pose2)
        move_together_func(target_place_pose1, target_place_pose2)
        open_gripper_together()
        move_together_func(pre_place_pose1, pre_place_pose2)
        self.las_gripper = 'dual'
        return arm_tag1, arm_tag2
        
    def check_success(self):
        block1_pose = self.block1.get_pose().p
        block2_pose = self.block2.get_pose().p
        block3_pose = self.block3.get_pose().p

        eps = [0.10,0.03,0.015]

        return np.all(abs(block1_pose[:2] - block2_pose[:2]) < eps[:2]) and \
               np.all(abs(block2_pose[:2] - block3_pose[:2]) < eps[:2]) and \
               block1_pose[0] < block2_pose[0] and block2_pose[0] < block3_pose[0] and \
               self.is_left_gripper_open() and self.is_right_gripper_open()