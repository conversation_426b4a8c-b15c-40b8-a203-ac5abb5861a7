from ._base_task import Base_Task
from .utils import *
import math
import sapien

class dual_shoes_place(Base_Task):
    def setup_demo(self,is_test = False, **kwags):
        super()._init(**kwags)
        self.bias -= 0.1
        self.create_table_and_wall()
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        self.id_list = [i for i in range(10)]
        self.load_actors()
        if self.messy_table:
            self.get_messy_table()
        self.step_lim = 600
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):
        self.shoe_box, self.shoe_box_data = create_actor(
            self.scene,
            pose = sapien.Pose([0,-0.13,0.741+self.bias],[0.5,0.5,-0.5,-0.5]),
            modelname="007_shoe_box",
            convex=True,
            is_static=True,
        )

        shoe_id = np.random.choice(self.id_list)
        self.shoe_id = shoe_id

        # left shoe
        shoes_pose = rand_pose(
            xlim=[-0.3,-0.2],
            ylim=[-0.1,0.05],
            zlim=[0.741+self.bias],
            ylim_prop=True,
            rotate_rand=True,
            rotate_lim=[0,3.14,0],
            qpos=[0.707,0.707,0,0]
        )

        while np.sum(pow(shoes_pose.get_p()[:2] - np.zeros(2),2)) < 0.0225:
            shoes_pose = rand_pose(
                xlim=[-0.3,-0.2],
                ylim=[-0.1,0.05],
                zlim=[0.741+self.bias],
                ylim_prop=True,
                rotate_rand=True,
                rotate_lim=[0,3.14,0],
                qpos=[0.707,0.707,0,0]
            )
        
        self.left_shoe, self.left_shoe_data = create_actor(
            self.scene,
            pose=shoes_pose,
            modelname="041_shoe",
            convex=True,
            model_id = shoe_id,
        )

        # right shoe
        shoes_pose = rand_pose(
            xlim=[0.2,0.3],
            ylim=[-0.1,0.05],
            zlim=[0.741+self.bias],
            ylim_prop=True,
            rotate_rand=True,
            rotate_lim=[0,3.14,0],
            qpos=[0.707,0.707,0,0]
        )

        while np.sum(pow(shoes_pose.get_p()[:2] - np.zeros(2),2)) < 0.0225:
            shoes_pose = rand_pose(
                xlim=[0.2,0.3],
                ylim=[-0.1,0.05],
                zlim=[0.741+self.bias],
                ylim_prop=True,
                rotate_rand=True,
                rotate_lim=[0,3.14,0],
                qpos=[0.707,0.707,0,0]
            )

        self.right_shoe, self.right_shoe_data = create_actor(
            self.scene,
            pose=shoes_pose,
            modelname="041_shoe",
            convex=True,
            model_id = shoe_id,
        )

        self.left_shoe.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.1
        self.right_shoe.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.1
        
        self.add_prohibit_area(self.left_shoe, self.left_shoe_data, padding=0.02)
        self.add_prohibit_area(self.right_shoe, self.right_shoe_data, padding=0.02)
        self.prohibited_area.append([-0.15,-0.25,0.15,0.01])

    def play_once(self):
        left_pose, right_pose = self._test_grasp_dual_actor(self.left_shoe, self.left_shoe_data, self.right_shoe, self.right_shoe_data, left_pre_grasp_dis=0.1, right_pre_grasp_dis=0.1)
        left_pose[2] += 0.15
        right_pose[2] += 0.15
        self.together_move_to_pose(left_target_pose=left_pose, right_target_pose = right_pose)

        left_target = self.get_actor_functional_pose(self.shoe_box, self.shoe_box_data, actor_functional_point_id=0)
        right_target = self.get_actor_functional_pose(self.shoe_box, self.shoe_box_data, actor_functional_point_id=1)
        left_target_place_pose = self._test_get_place_pose(self.left_shoe, self.left_shoe_data, left_target, arm_tag='left', constrain = 'align', pre_dis = 0.05, functional_point_id=0)
        right_target_place_pose = self._test_get_place_pose(self.right_shoe, self.right_shoe_data, right_target, arm_tag='right', constrain = 'align', pre_dis = 0.05, functional_point_id=0)
        right_temp_pose = [0.25,-0.07,right_pose[2]] + right_target_place_pose[-4:]

        self.together_move_to_pose(left_target_pose = left_target_place_pose, right_target_pose=right_temp_pose)
        left_target_place_pose[2] -= 0.07
        self.left_move_to_pose(pose = left_target_place_pose, constraint_pose=[1,1,1,0,0,0])
        self.open_left_gripper()
        left_target_place_pose[2] += 0.07
        self.left_move_to_pose(pose = left_target_place_pose, constraint_pose=[1,1,1,0,0,0])

        left_target_place_pose[0] = -0.25
        self.together_move_to_pose(left_target_pose=left_target_place_pose, right_target_pose = right_target_place_pose)
        right_target_place_pose[2] -= 0.07
        self.right_move_to_pose(pose = right_target_place_pose, constraint_pose=[1,1,1,0,0,0])
        self.open_right_gripper()
        right_target_place_pose[2] += 0.07
        self.right_move_to_pose(pose = right_target_place_pose, constraint_pose=[1,1,1,0,0,0])
        self.delay(3)
            
        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        info['info'] = {"{A}": f"041_shoe/base{self.shoe_id}", "{B}": f"007_shoe_box/base0"}
        return info
    
    def check_success(self):
        left_shoe_pose_p = np.array(self.left_shoe.get_pose().p)
        left_shoe_pose_q = np.array(self.left_shoe.get_pose().q)
        right_shoe_pose_p = np.array(self.right_shoe.get_pose().p)
        right_shoe_pose_q = np.array(self.right_shoe.get_pose().q)
        if left_shoe_pose_q[0] < 0:
            left_shoe_pose_q *= -1
        if right_shoe_pose_q[0] < 0:
            right_shoe_pose_q *= -1
        target_pose_p = np.array([0,-0.13])
        target_pose_q = np.array([0.5,0.5,-0.5,-0.5])
        eps = np.array([0.05,0.05,0.07,0.07,0.07,0.07])
        return np.all(abs(left_shoe_pose_p[:2] - (target_pose_p - [0,0.04])) < eps[:2]) and np.all(abs(left_shoe_pose_q - target_pose_q) < eps[-4:]) and \
               np.all(abs(right_shoe_pose_p[:2] - (target_pose_p + [0,0.04])) < eps[:2]) and np.all(abs(right_shoe_pose_q - target_pose_q) < eps[-4:]) and \
               abs(left_shoe_pose_p[2] - (0.74+self.bias)) < 0.03 and abs(right_shoe_pose_p[2] - (0.74  + self.bias)) < 0.03 and self.is_left_gripper_open() and self.is_right_gripper_open()