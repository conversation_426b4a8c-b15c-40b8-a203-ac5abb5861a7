from ._base_task import Base_Task
from .utils import *
import sapien
from copy import deepcopy   

class place_phone_stand(Base_Task):
    def setup_demo(self,is_test = False,**kwags):
        super()._init(**kwags)
        self.create_table_and_wall()
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        self.load_actors()
        if self.messy_table:
            self.get_messy_table()
        self.step_lim = 400
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):
        tag = np.random.randint(2)
        ori_quat = [[0.707,0.707,0,0], [0.5,0.5,0.5,0.5],[0.5,0.5,-0.5,-0.5],[0.5,0.5,-0.5,-0.5], [0.5,-0.5,0.5,-0.5]]
        if tag == 0:
            phone_x_lim = [-0.25, -0.05]
            stand_x_lim = [-0.15, 0.]
        else:
            phone_x_lim = [0.05, 0.25]
            stand_x_lim = [0, 0.15]

        self.phone_id = np.random.choice([0,1,2,4],1)[0]
        phone_pose = rand_pose(
            xlim= phone_x_lim,
            ylim=[-0.2, 0.],
            zlim=[0.75+self.bias],
            qpos=ori_quat[self.phone_id],
            rotate_rand=True,
            rotate_lim=[0,0.7,0],
        )
        self.phone, self.phone_data= create_actor(
            scene=self.scene,
            pose  = phone_pose,
            modelname="077_phone",
            convex=True,
            model_id=self.phone_id
        )
        self.phone.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01
        stand_pose = rand_pose(
            xlim = stand_x_lim, 
            ylim=[0, 0.2],
            zlim=[0.741+self.bias], 
            qpos=[0.707,0.707,0,0],
            rotate_rand=False,
        )
        while np.sqrt(np.sum((phone_pose.p[:2]-stand_pose.p[:2])**2))<0.2:
            stand_pose = rand_pose(
                xlim = stand_x_lim, 
                ylim=[0, 0.2],
                zlim=[0.741+self.bias], 
                qpos=[0.707,0.707,0,0],
                rotate_rand=False,
            )
        
        self.stand_id = np.random.choice([1,2],1)[0]
        self.stand, self.stand_data= create_actor(
            scene=self.scene,
            pose  = stand_pose,
            modelname="078_phonestand",
            convex=True,
            model_id=self.stand_id,
            is_static=True
        )
        self.add_prohibit_area(self.phone, self.phone_data, padding=0.2)
        self.add_prohibit_area(self.stand, self.stand_data, padding=0.2)

    def play_once(self):
        if self.phone.get_pose().p[0] < 0:
            arm_tag = 'left'
            move_func = self.left_move_to_pose
            close_func = self.close_left_gripper
            open_func = self.open_left_gripper
        else:
            arm_tag = 'right'
            move_func = self.right_move_to_pose
            close_func = self.close_right_gripper
            open_func = self.open_right_gripper

        pose = self._test_grasp_single_actor(self.phone, self.phone_data, arm_tag = arm_tag, pre_grasp_dis=0.08)
        pose[2] += 0.1
        pose[0] = -0.25 if arm_tag == 'left' else 0.25
        pose[1] = -0.2
        pose[-4:] = [0.707,0,0,0.707]
        move_func(pose)
        stand_func_pose = self.get_actor_functional_pose(self.stand, self.stand_data, 0)
        pre_target_pose = self.get_grasp_pose_from_goal_point_and_direction(self.phone, self.phone_data, endpose_tag=arm_tag, actor_functional_point_id=0, target_point=stand_func_pose[:3],
                                                                            target_approach_direction=stand_func_pose[-4:], pre_dis=-0.02)
        pre_target_pose[1] -= 0.01
        target_pose = self.get_grasp_pose_from_goal_point_and_direction(self.phone, self.phone_data, endpose_tag=arm_tag, actor_functional_point_id=0, target_point=stand_func_pose[:3],
                                                                        target_approach_direction=stand_func_pose[-4:], pre_dis=0.005)
        move_func(pre_target_pose)
        move_func(target_pose, constraint_pose=[1,1,1,0,0,0])
        open_func()
        target_pose[1] -= 0.03
        target_pose[2] += 0.03
        move_func(target_pose)

        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        info['info'] = {'{A}': f'077_phone/base{self.phone_id}', '{B}': f'078_phonestand/base{self.stand_id}'}
        return info

    def check_success(self):
        phone_func_pose = np.array(self.get_actor_functional_pose(self.phone, self.phone_data, 0))
        stand_func_pose = np.array(self.get_actor_functional_pose(self.stand, self.stand_data, 0))
        stand_pos = self.stand.get_pose().p
        eps = np.array([0.045, 0.02, 0.02])
        return np.all(np.abs(phone_func_pose - stand_func_pose)[:3] < eps) and self.is_left_gripper_open() and self.is_right_gripper_open()