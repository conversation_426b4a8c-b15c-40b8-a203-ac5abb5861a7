from ._base_task import Base_Task
from .utils import *
import sapien
from copy import deepcopy

class put_bottles_dustbin(Base_Task):
    def setup_demo(self,**kwags):
        super()._init(**kwags)
        self.create_table_and_wall([0.3,0])
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        self.load_actors()
        # if self.messy_table:
        #    self.get_messy_table()
        self.step_lim = 1000
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_close_gripper(save_freq=None)
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):
        pose_lst = []
        def create_bottle(model_id):
            bottle_pose = rand_pose(
                xlim=[-0.25, 0.3],
                ylim=[0.03,0.23],
                zlim=[0.741+self.bias],
                rotate_rand=False,
                rotate_lim=[0,1,0],
                qpos=[0.707,0.707,0,0],
            )
            tag = True
            gen_lim = 100
            i = 1
            while tag and i < gen_lim:
                tag = False
                if np.abs(bottle_pose.p[0]) < 0.05: tag = True
                for pose in pose_lst:
                    if np.sum(np.power(np.array(pose[:2])-np.array(bottle_pose.p[:2]),2)) < 0.0169:
                        tag = True
                        break
                if tag:
                    i += 1
                    bottle_pose = rand_pose(
                        xlim=[-0.25, 0.3],
                        ylim=[0.03,0.23],
                        zlim=[0.741+self.bias],
                        rotate_rand=False,
                        rotate_lim=[0,1,0],
                        qpos=[0.707,0.707,0,0],
                    )
            pose_lst.append(bottle_pose.p[:2])
            bottle, bottle_data = create_actor(
                self.scene,
                bottle_pose,
                modelname="151_bottle",
                convex=True,
                model_id=model_id
            )
            bottle.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.01
            return bottle, bottle_data
        
        self.bottles = []
        self.bottles_data = []
        self.bottle_id = [3616, 3822, 13]
        self.bottle_num = 3
        for i in range(self.bottle_num):
            bottle, bottle_data = create_bottle(self.bottle_id[i])
            self.bottles.append(bottle)
            self.bottles_data.append(bottle_data)
            self.add_prohibit_area(bottle, bottle_data, padding=0.1)

        self.dustbin, self.dustbin_data = create_actor(
            self.scene,
            pose=sapien.Pose([-0.45, 0, 0],[0.5, 0.5, 0.5, 0.5]),
            modelname="150_dustbin",
            convex=True,
            is_static=True
        )
        self.delay(2)

    def play_once(self):
        bottle_lst, bottle_lst_data = zip(*sorted(zip(self.bottles, self.bottles_data), key=lambda x: [x[0].get_pose().p[0]>0, x[0].get_pose().p[1]]))

        las_arm_tag = None
        for i in range(self.bottle_num):
            bottle = bottle_lst[i]
            bottle_data = bottle_lst_data[i]
            arm_tag = 'left' if bottle.get_pose().p[0] < 0 else 'right'

            delta_dis = 0.05

            left_end_pose = [-0.35, -0.1, 0.93, 0.65, -0.25, 0.25, 0.65]
            if arm_tag == 'left':
                grasp_pose = self._test_grasp_single_actor(bottle, bottle_data, arm_tag='left', pre_grasp_dis=0.1)
                grasp_pose[2] += 0.1
                self.left_move_to_pose(grasp_pose)
                self.left_move_to_pose(left_end_pose)
                las_arm_tag = 'left'
            else:
                pre_grasp_pose, grasp_pose = self._test_choose_grasp_pose(bottle, bottle_data, pre_dis=0.1, arm_tag=arm_tag)
                pre_grasp_pose[2] += delta_dis
                grasp_pose[2] += delta_dis
                if las_arm_tag is not None:
                    self.together_move_to_pose(self.robot.left_original_pose, pre_grasp_pose)
                else:
                    self.right_move_to_pose(pre_grasp_pose)
                self.right_move_to_pose(grasp_pose)
                self.close_right_gripper()
                grasp_pose[2] += 0.1
                self.right_move_to_pose(grasp_pose)
                self._test_place(bottle, bottle_data, target_pose = [0, 0., 0.88, 0, 1, 0,0], arm_tag='right', functional_point_id=0, pre_dis = 0, dis = 0, is_open=False, constrain='align')
                pre_grasp_pose, grasp_pose = self._test_choose_grasp_pose(bottle, bottle_data, pre_dis=0.1, arm_tag='left')
                pre_grasp_pose[2] -= delta_dis
                grasp_pose[2] -= delta_dis
                self.left_move_to_pose(pre_grasp_pose)
                self.left_move_to_pose(grasp_pose)
                self.close_left_gripper()
                self.open_right_gripper()
                self.together_move_to_pose(left_end_pose, self.robot.right_original_pose)
                las_arm_tag = 'right'
            self.open_left_gripper()
       
        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        info['info'] = {"{A}": f"151_bottle/base{self.bottle_id[0]}", "{B}": f"151_bottle/base{self.bottle_id[1]}", "{C}": f"151_bottle/base{self.bottle_id[2]}", "{D}": f"150_dustbin/base0"}
        return info

    def stage_reward(self):
        taget_pose = [-0.45, 0]
        eps = np.array([0.221, 0.325])
        reward = 0
        reward_step = 1/3
        for i in range(self.bottle_num):
            bottle_pose = self.bottles[i].get_pose().p
            if np.all(np.abs(bottle_pose[:2] - taget_pose) < eps) and bottle_pose[2] > 0.2 and bottle_pose[2] < 0.7:
                reward += reward_step
        return reward
    
    def check_success(self):
        taget_pose = [-0.45, 0]
        eps = np.array([0.221, 0.325])
        for i in range(self.bottle_num):
            bottle_pose = self.bottles[i].get_pose().p
            if np.all(np.abs(bottle_pose[:2] - taget_pose) < eps) and bottle_pose[2] > 0.2 and bottle_pose[2] < 0.7:
                continue
            return False
        return True
