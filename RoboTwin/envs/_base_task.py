import sapien.core as sapien
from sapien.utils.viewer import <PERSON><PERSON>
import numpy as np
import gymnasium as gym
import pdb
import numpy as np
import toppra as ta
import json
import transforms3d as t3d
from collections import OrderedDict
import torch, random

from .utils import *
import math
from .robot import Robot
from .camera import Camera

from copy import deepcopy
import subprocess
from pathlib import Path
import trimesh
from ._GLOBAL_CONFIGS import *

current_file_path = os.path.abspath(__file__)
parent_directory = os.path.dirname(current_file_path)

class Base_Task(gym.Env):
    def __init__(self):
        pass

    def _init(self, **kwags):
        '''
            Initialization
            - `self.PCD_INDEX`: The index of the file saved for the current scene.
            - `self.fcitx5-configtool`: Left gripper pose (close <=0, open >=0.4).
            - `self.ep_num`: Episode ID.
            - `self.task_name`: Task name.
            - `self.save_dir`: Save path.`
            - `self.left_original_pose`: Left arm original pose.
            - `self.right_original_pose`: Right arm original pose.
            - `self.left_arm_joint_id`: [6,14,18,22,26,30].
            - `self.right_arm_joint_id`: [7,15,19,23,27,31].
            - `self.render_fre`: Render frequency.
        '''
        super().__init__()
        ta.setup_logging("CRITICAL") # hide logging
        np.random.seed(kwags.get('seed', 0))
        torch.manual_seed(kwags.get('seed', 0))
        # random.seed(kwags.get('seed', 0))

        self.PCD_INDEX = 0
        self.task_name = kwags.get('task_name')
        self.save_dir = kwags.get('save_path', 'data')
        self.ep_num = kwags.get('now_ep_num', 0)
        self.render_freq = kwags.get('render_freq', 10)
        self.data_type = kwags.get('data_type', None)
        self.is_save = kwags.get('is_save', False)
        self.dual_arm = kwags.get('dual_arm', True)
        self.eval_mode = kwags.get('eval_mode', False)

        self.need_topp = True

        # Random
        random_setting = kwags.get("augmentation")
        self.random_background = random_setting.get('random_background', False)
        self.messy_table = random_setting.get('messy_table', False)
        self.clean_background_rate = random_setting.get('clean_background_rate', 1)
        self.random_head_camera_dis = random_setting.get('random_head_camera_dis', 0)
        self.random_table_height = random_setting.get('random_table_height', 0)
        self.random_light = random_setting.get('random_light', False)
        self.crazy_random_light_rate = random_setting.get('crazy_random_light_rate', 0)
        self.crazy_random_light = 0 if not self.random_light else np.random.rand() < self.crazy_random_light_rate
        self.random_embodiment = random_setting.get('random_embodiment', False) # TODO

        self.file_path = []
        self.plan_success = True
        self.step_lim = None
        self.fix_gripper = False
        self.setup_scene()

        self.left_js = None
        self.right_js = None
        self.raw_head_pcl = None
        self.real_head_pcl = None
        self.real_head_pcl_color = None

        self.now_obs = {}
        self.take_action_cnt = 0
        self.eval_video_path = kwags.get('eval_video_save_dir', None)

        self.save_freq = kwags.get('save_freq')
        self.world_pcd = None

        self.size_dict = list()
        self.messy_objs = list()
        self.prohibited_area = list() # [x1, y1,x2, y2]
        self.record_messy_objects = list() # record messy objects

        self.eval_success = False
        self.bias = np.random.uniform(low=-self.random_table_height, high=0) # TODO
        self.need_plan = kwags.get('need_plan', True)
        self.left_path_lst = kwags.get('left_path_lst', [])
        self.right_path_lst = kwags.get('right_path_lst',[])
        self.left_cnt = 0
        self.right_cnt = 0

        self.instruction = None # for eval

        # Abandoned
        self.table_static = kwags.get('table_static', True)
        # self.episode_score = 0

    def set_path_lst(self, args):
        self.need_plan = args.get('need_plan', True)
        self.left_path_lst = args.get('left_path_lst', [])
        self.right_path_lst = args.get('right_path_lst',[])

    def _del_eval_video_ffmpeg(self):
        if self.eval_video_ffmpeg:
            self.eval_video_ffmpeg.stdin.close()
            self.eval_video_ffmpeg.wait()
            del self.eval_video_ffmpeg

    def _set_eval_video_ffmpeg(self,  ffmpeg):
        self.eval_video_ffmpeg = ffmpeg

    def play_once(self):
        pass

    def check_success(self):
        pass

    def pre_move(self):
        pass

    def setup_scene(self,**kwargs):
        '''
        Set the scene
            - Set up the basic scene: light source, viewer.
        '''
        # Import required modules at the beginning to avoid scoping issues
        import sapien.core
        import sapien.physx
        from sapien.render import set_global_config

        self.engine = sapien.Engine()
        # declare sapien renderer
        set_global_config(max_num_materials = 50000, max_num_textures = 50000)

        # Try to create renderer, fallback to headless mode if failed
        try:
            self.renderer = sapien.SapienRenderer()
            # give renderer to sapien sim
            self.engine.set_renderer(self.renderer)

            sapien.render.set_camera_shader_dir("rt")
            sapien.render.set_ray_tracing_samples_per_pixel(32)
            sapien.render.set_ray_tracing_path_depth(8)
            sapien.render.set_ray_tracing_denoiser("oidn")
            self.headless_mode = False
        except RuntimeError as e:
            if "failed to find a rendering device" in str(e):
                print("⚠️  渲染设备不可用，切换到无头模式")
                self.renderer = None
                self.headless_mode = True
                # Force render_freq to 0 in headless mode
                self.render_freq = 0
            else:
                raise e

        # declare sapien scene
        scene_config = sapien.SceneConfig()
        if self.headless_mode:
            # In headless mode, create scene without render system
            try:
                # Try to create scene with only physics system
                self.scene = sapien.core.Scene([sapien.physx.PhysxCpuSystem()])
            except:
                # Fallback to default scene creation
                self.scene = self.engine.create_scene(scene_config)
        else:
            self.scene = self.engine.create_scene(scene_config)
        # set simulation timestep
        self.scene.set_timestep(kwargs.get("timestep", 1 / 250))
        # add ground to scene (skip in headless mode if it causes issues)
        if not self.headless_mode:
            try:
                self.scene.add_ground(kwargs.get("ground_height", 0))
            except RuntimeError as e:
                if "failed to find a rendering device" in str(e):
                    print("⚠️  跳过地面创建（渲染设备问题）")
                else:
                    raise e
        else:
            print("⚠️  跳过地面创建（无头模式）")
        # set default physical material
        self.scene.default_physical_material = self.scene.create_physical_material(
            kwargs.get("static_friction", 0.5),
            kwargs.get("dynamic_friction", 0.5),
            kwargs.get("restitution", 0),
        )
        # give some white ambient light of moderate intensity (skip in headless mode)
        if not self.headless_mode:
            try:
                self.scene.set_ambient_light(kwargs.get("ambient_light", [0.5, 0.5, 0.5]))
                # default enable shadow unless specified otherwise
                shadow = kwargs.get("shadow", True)
                # default spotlight angle and intensity
                direction_lights = kwargs.get(
                    "direction_lights", [[[0, 0.5, -1], [0.5, 0.5, 0.5]]]
                )
                self.direction_light_lst = []
                for direction_light in direction_lights:
                    if self.random_light:
                        direction_light[1] = [np.random.rand(), np.random.rand(),np.random.rand()]
                    self.direction_light_lst.append(self.scene.add_directional_light(direction_light[0], direction_light[1], shadow=shadow))
                # default point lights position and intensity
                point_lights = kwargs.get(
                    "point_lights",
                    [[[1, 0, 1.8], [1, 1, 1]], [[-1, 0, 1.8], [1, 1, 1]]]
                )
                self.point_light_lst = []
                for point_light in point_lights:
                    if self.random_light:
                        point_light[1] = [np.random.rand(), np.random.rand(),np.random.rand()]
                    self.point_light_lst.append(self.scene.add_point_light(point_light[0], point_light[1], shadow=shadow))
            except RuntimeError as e:
                if "failed to get system" in str(e):
                    print("⚠️  跳过光照设置（无头模式）")
                    self.direction_light_lst = []
                    self.point_light_lst = []
                else:
                    raise e
        else:
            print("⚠️  跳过光照设置（无头模式）")
            self.direction_light_lst = []
            self.point_light_lst = []

        # initialize viewer with camera position and orientation
        if self.render_freq and not self.headless_mode:
            self.viewer = Viewer(self.renderer)
            self.viewer.set_scene(self.scene)
            self.viewer.set_camera_xyz(
                x=kwargs.get("camera_xyz_x", 0.4),
                y=kwargs.get("camera_xyz_y", 0.22),
                z=kwargs.get("camera_xyz_z", 1.5),
            )
            self.viewer.set_camera_rpy(
                r=kwargs.get("camera_rpy_r", 0),
                p=kwargs.get("camera_rpy_p", -0.8),
                y=kwargs.get("camera_rpy_y", 2.45),
            )
        else:
            self.viewer = None

    def create_table_and_wall(self, table_bias=[0,0], table_height=0.74):
        # creat wall
        self.table_bias = table_bias
        wall_texture, table_texture = None, None
        table_height += self.bias

        if self.random_background:
            texture_type = "seen" if not self.eval_mode else "unseen"
            directory_path = f"./assets/background_texture/{texture_type}"
            file_count = len([name for name in os.listdir(directory_path) if os.path.isfile(os.path.join(directory_path, name))])

            wall_texture, table_texture = random.randint(0, file_count - 1), random.randint(0, file_count - 1)
            self.wall_texture, self.table_texture = f'{texture_type}/{wall_texture}', f'{texture_type}/{table_texture}'
            if np.random.rand() <= self.clean_background_rate:
                self.wall_texture = None
            if np.random.rand() <= self.clean_background_rate:
                self.table_texture = None
        else:
            self.wall_texture, self.table_texture = None, None

        # Skip wall and table creation in headless mode to avoid rendering issues
        if hasattr(self, 'headless_mode') and self.headless_mode:
            print("⚠️  跳过墙和桌子创建（无头模式）")
            self.wall = None
            self.table = None
            return
        else:
            try:
                self.wall = create_box(
                    self.scene,
                    sapien.Pose(p=[0, 1, 1.5]),
                    half_size=[3, 0.6, 1.5],
                    color=(1, 0.9, 0.9),
                    name='wall',
                    texture_id=self.wall_texture
                )

                # creat table
                self.table = create_table(
                    self.scene,
                    sapien.Pose(p = [table_bias[0], table_bias[1], table_height]),
                    length=1.2,
                    width=0.7,
                    height=table_height,
                    thickness=0.05,
                    is_static=self.table_static,
                    texture_id=self.table_texture
                )
            except RuntimeError as e:
                if "failed to find a rendering device" in str(e):
                    print("⚠️  跳过墙和桌子创建（渲染设备问题）")
                    self.wall = None
                    self.table = None
                else:
                    raise e

    def load_robot(self, **kwags):
        """
            load aloha robot urdf file, set root pose and set joints
        """

        if not hasattr(self, 'robot'):
            try:
                self.robot = Robot(self.scene, self.need_topp, **kwags)
                self.robot.set_planner(self.scene)
                self.robot.init_joints()
            except RuntimeError as e:
                if "failed to find a rendering device" in str(e) and hasattr(self, 'headless_mode') and self.headless_mode:
                    print("⚠️  跳过机器人加载（无头模式渲染问题）")
                    self.robot = None
                    return
                else:
                    raise e
        else:
            try:
                self.robot.reset(self.scene, self.need_topp, **kwags)
            except RuntimeError as e:
                if "failed to find a rendering device" in str(e) and hasattr(self, 'headless_mode') and self.headless_mode:
                    print("⚠️  跳过机器人重置（无头模式渲染问题）")
                    self.robot = None
                    return
                else:
                    raise e

        if self.robot is not None:
            for link in self.robot.left_entity.get_links():
                link:sapien.physx.PhysxArticulationLinkComponent = link
                link.set_mass(1)
            for link in self.robot.right_entity.get_links():
                link:sapien.physx.PhysxArticulationLinkComponent = link
                link.set_mass(1)

    def load_camera(self, **kwags):
        '''
            Add cameras and set camera parameters
                - Including four cameras: left, right, front, head.
        '''
        try:
            self.cameras = Camera(bias=self.bias, random_head_camera_dis=self.random_head_camera_dis, **kwags)
            self.cameras.load_camera(self.scene)
            self.scene.step()  # run a physical step
            self.scene.update_render()  # sync pose from SAPIEN to renderer
        except RuntimeError as e:
            if "failed to get system" in str(e) and hasattr(self, 'headless_mode') and self.headless_mode:
                print("⚠️  跳过相机加载（无头模式渲染问题）")
                self.cameras = None
            else:
                raise e

        # TODO
        # self.world_pcd = self.cameras.get_world_pcd()
        # save_pcd('./test_world_pcd.pcd', self.world_pcd, color=True)
        # self.robot.update_world_pcd(self.world_pcd[:,:3])

    def _update_render(self):
        """
            Update rendering to refresh the camera's RGBD information
            (rendering must be updated even when disabled, otherwise data cannot be collected).
        """
        if self.crazy_random_light:
            for renderColor in self.point_light_lst:
                renderColor.set_color([np.random.rand(), np.random.rand(),np.random.rand()])
            for renderColor in self.direction_light_lst:
                renderColor.set_color([np.random.rand(), np.random.rand(),np.random.rand()])
            now_ambient_light = self.scene.ambient_light
            now_ambient_light = np.clip(np.array(now_ambient_light) + np.random.rand(3) * 0.2 - 0.1, 0, 1)
            self.scene.set_ambient_light(now_ambient_light)
        self.cameras.update_wrist_camera(self.robot.left_camera.get_pose(), self.robot.right_camera.get_pose())
        self.scene.update_render()

    # 延时操作
    def delay(self, delay_time, save_freq = None):
        render_freq = self.render_freq
        self.render_freq=0
        left_gripper_val = self.robot.get_left_gripper_val()
        right_gripper_val = self.robot.get_right_gripper_val()
        for i in range(delay_time):
            self.together_close_gripper(save_freq=save_freq, left_pos=left_gripper_val, right_pos=right_gripper_val)
        self.render_freq = render_freq

    def set_gripper(self, set_tag = 'together', left_pos = None, right_pos = None, save_freq=-1):
        '''
            Set gripper posture
            - `left_pos`: Left gripper pose
            - `right_pos`: Right gripper pose
            - `set_tag`: "left" to set the left gripper, "right" to set the right gripper, "together" to set both grippers simultaneously.
        '''
        save_freq = self.save_freq if save_freq == -1 else save_freq
        if save_freq != None:
            self._take_picture()

        left_gripper_step = 0
        right_gripper_step = 0
        step_n = 0
        if set_tag == 'left' or set_tag == 'together':
            # left_result = self.robot.left_planner.plan_grippers(self.robot.get_left_gripper_real_val(), left_pos)
            left_result = self.robot.left_planner.plan_grippers(self.robot.get_left_gripper_val(), left_pos)
            left_gripper_step = left_result['step']
            left_gripper_res = left_result['result']
            step_n = left_result['step_n']

        if set_tag == 'right' or set_tag == 'together':
            # right_result = self.robot.right_planner.plan_grippers(self.robot.get_right_gripper_real_val(), right_pos)
            right_result = self.robot.right_planner.plan_grippers(self.robot.get_right_gripper_val(), right_pos)
            right_gripper_step = right_result['step']
            right_gripper_res = right_result['result']
            step_n = right_result['step_n']

        alpha = 1.5
        # for i in range(int(step_n * 3 // 2)): # TODO
        for i in range(int(step_n * alpha)):
            if set_tag == 'left' or set_tag == 'together':
                self.robot.set_gripper(left_gripper_res[min(i, step_n-1)], 'left', left_gripper_step)

            if set_tag == 'right' or set_tag == 'together':
                self.robot.set_gripper(right_gripper_res[min(i, step_n-1)], 'right', right_gripper_step)

            self.scene.step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                if self.viewer:
                    if self.viewer: self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        if save_freq != None:
            self._take_picture()

    def open_left_gripper(self, save_freq=-1, pos = 1):
        self.set_gripper(left_pos = pos, set_tag='left', save_freq=save_freq)

    def close_left_gripper(self, save_freq=-1, pos = 0.15):
        self.set_gripper(left_pos = pos, set_tag='left',save_freq=save_freq)

    def open_right_gripper(self, save_freq=-1,pos = 1):
        self.set_gripper(right_pos=pos, set_tag='right', save_freq=save_freq)

    def close_right_gripper(self, save_freq=-1,pos = 0.15):
        self.set_gripper(right_pos=pos, set_tag='right', save_freq=save_freq)

    def together_open_gripper(self, save_freq=-1, left_pos = 1, right_pos = 1):
        self.set_gripper(left_pos=left_pos, right_pos=right_pos, set_tag='together', save_freq=save_freq)

    def together_close_gripper(self, save_freq=-1,left_pos = 0.15, right_pos = 0.15):
        self.set_gripper(left_pos=left_pos, right_pos=right_pos, set_tag='together', save_freq=save_freq)

    def left_move_to_pose(self, pose, constraint_pose = None, use_point_cloud=False, use_attach=False,save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        if not self.plan_success:
            return
        if pose is None:
            self.plan_success = False
            return
        if type(pose) == sapien.Pose:
            pose = pose.p.tolist() + pose.q.tolist()
        save_freq = self.save_freq if save_freq == -1 else save_freq
        if self.need_plan:
            left_result = self.robot.left_plan_path(pose, constraint_pose=constraint_pose)
            self.left_path_lst.append(deepcopy(left_result))
        else:
            left_result = deepcopy(self.left_path_lst[self.left_cnt])
            self.left_cnt += 1

        if left_result["status"] != "Success":
            self.plan_success = False
            return

        if save_freq != None:
            self._take_picture()

        n_step = left_result["position"].shape[0]
        for i in range(n_step):
            self.robot.set_arm_joints(left_result['position'][i], left_result['velocity'][i], 'left')
            self.scene.step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                if self.viewer: self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        if save_freq != None:
            self._take_picture()

    def right_move_to_pose(self, pose, constraint_pose = None, use_point_cloud=False, use_attach=False, save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        if not self.plan_success:
            return
        if pose is None:
            self.plan_success = False
            return
        if type(pose) == sapien.Pose:
            pose = pose.p.tolist() + pose.q.tolist()
        save_freq = self.save_freq if save_freq == -1 else save_freq
        if self.need_plan:
            right_result = self.robot.right_plan_path(pose, constraint_pose=constraint_pose)
            self.right_path_lst.append(deepcopy(right_result))
        else:
            right_result = deepcopy(self.right_path_lst[self.right_cnt])
            self.right_cnt += 1

        if right_result["status"] != "Success":
            self.plan_success = False
            return

        if save_freq != None:
            self._take_picture()

        n_step = right_result["position"].shape[0]
        for i in range(n_step):
            self.robot.set_arm_joints(right_result['position'][i], right_result['velocity'][i], 'right')
            self.scene.step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                if self.viewer: self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        if save_freq != None:
            self._take_picture()

    def together_move_to_pose(self, left_target_pose, right_target_pose, left_constraint_pose = None, right_constraint_pose = None,
                              use_point_cloud=False, use_attach=False, save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        if not self.plan_success:
            return
        if left_target_pose is None or right_target_pose is None:
            self.plan_success = False
            return
        if type(left_target_pose) == sapien.Pose:
            left_target_pose = left_target_pose.p.tolist() + left_target_pose.q.tolist()
        if type(right_target_pose) == sapien.Pose:
            right_target_pose = right_target_pose.p.tolist() + right_target_pose.q.tolist()
        save_freq = self.save_freq if save_freq == -1 else save_freq
        if self.need_plan:
            left_result = self.robot.left_plan_path(left_target_pose, constraint_pose=left_constraint_pose)
            right_result = self.robot.right_plan_path(right_target_pose, constraint_pose=right_constraint_pose)
            self.left_path_lst.append(deepcopy(left_result))
            self.right_path_lst.append(deepcopy(right_result))
        else:
            left_result = deepcopy(self.left_path_lst[self.left_cnt])
            right_result = deepcopy(self.right_path_lst[self.right_cnt])
            self.left_cnt += 1
            self.right_cnt += 1

        try:
            left_success = left_result["status"] == "Success"
            right_success = right_result["status"] == "Success"
            if not left_success or not right_success:
                self.plan_success = False
                # return TODO
        except Exception as e:
            if left_result is None or right_result is None:
                self.plan_success = False
                return #TODO

        if save_freq != None:
            self._take_picture()

        now_left_id = 0
        now_right_id = 0
        i = 0

        left_n_step = left_result["position"].shape[0] if left_success else 0
        right_n_step = right_result["position"].shape[0] if right_success else 0

        while now_left_id < left_n_step or now_right_id < right_n_step:
            # set the joint positions and velocities for move group joints only.
            # The others are not the responsibility of the planner
            if left_success and now_left_id < left_n_step and (not right_success or now_left_id / left_n_step <= now_right_id / right_n_step):
                self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id], 'left')
                now_left_id +=1

            if right_success and now_right_id < right_n_step and (not left_success or now_right_id / right_n_step <= now_left_id / left_n_step):
                self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id], 'right')
                now_right_id +=1

            self.scene.step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                if self.viewer: self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()
            i+=1

        if save_freq != None:
            self._take_picture()

    def is_left_gripper_open(self):
        return self.robot.is_left_gripper_open()

    def is_right_gripper_open(self):
        return self.robot.is_right_gripper_open()

    def is_left_gripper_open_half(self):
        return self.robot.is_left_gripper_open_half()

    def is_right_gripper_open_half(self):
        return self.robot.is_right_gripper_open_half()

    def is_left_gripper_close(self):
        return self.robot.is_left_gripper_close()

    def is_right_gripper_close(self):
        return self.robot.is_right_gripper_close()

    # =========================================================== New APIS ===========================================================
    def get_scene_contact(self):
        contacts = self.scene.get_contacts()
        for contact in contacts:
            pdb.set_trace()
            print(dir(contact))
            print(contact.bodies[0].entity.name, contact.bodies[1].entity.name)

    def choose_best_pose(self, res_pose, center_pose, arm_tag = None):
        """
            Choose the best pose from the list of target poses.
            - target_lst: List of target poses.
        """
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        if arm_tag == 'left':
            plan_multi_pose = self.robot.left_plan_multi_path
        elif arm_tag == 'right':
            plan_multi_pose = self.robot.right_plan_multi_path
        target_lst = self.robot.create_target_pose_list(res_pose, center_pose, arm_tag)
        pose_num = len(target_lst)
        traj_lst = plan_multi_pose(target_lst)
        now_pose = None
        now_step = -1
        for i in range(pose_num):
            if traj_lst['status'][i] != "Success": continue
            if now_pose is None or len(traj_lst['position'][i]) < now_step:
                now_pose = target_lst[i]
        return now_pose

    # test grasp pose of all contact points
    def _print_all_grasp_pose_of_contact_points(self, actor, actor_data, pre_dis = 0.1):
        for i in range(len(actor_data['contact_points_pose'])):
            print(i, self.get_grasp_pose_w_labeled_direction(actor, actor_data, pre_dis=pre_dis, contact_point_id=i))

    # def get_target_pose_from_goal_point_and_gripper_direction(self, actor, actor_data = None, arm_tag = None, target_pose = None, target_grasp_qpose = None):
    #     """
    #         Obtain the grasp pose through the given target point and contact direction.
    #         - actor: The instance of the object to be grasped.
    #         - actor_data: The annotation data corresponding to the instance of the object to be grasped.
    #         - endpose: The end effector pose, from robot.get_left_ee_pose() or robot.get_right_ee_pose().
    #         - target_pose: The target point coordinates for aligning the functional points of the object to be grasped.
    #         - target_grasp_qpose: The direction of the grasped object's contact target point,
    #                              represented as a quaternion in the world coordinate system.
    #     """
    #     endpose = self.robot.get_left_ee_pose() if arm_tag == 'left' else self.robot.get_right_ee_pose()
    #     actor_matrix = actor.get_pose().to_transformation_matrix()
    #     local_target_matrix = np.asarray(actor_data['target_pose'][0])
    #     local_target_matrix[:3,3] *= actor_data['scale']
    #     res_matrix = np.eye(4)
    #     res_matrix[:3,3] = (actor_matrix  @ local_target_matrix)[:3,3] - endpose[:3]
    #     # @ np.array([[1,0,0],[0,-1,0],[0,0,-1]])
    #     res_matrix[:3,3] = np.linalg.inv(t3d.quaternions.quat2mat(endpose[-4:])) @ res_matrix[:3,3]
    #     res_pose = list(target_pose - t3d.quaternions.quat2mat(target_grasp_qpose) @ res_matrix[:3,3]) + target_grasp_qpose
    #     return res_pose

    def get_grasp_pose_w_labeled_direction(self, actor, actor_data, pre_dis = 0., contact_point_id = 0, arm_tag = None, is_z_rotation = False):
        """
            Obtain the grasp pose through the marked grasp point.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - pre_dis: The distance in front of the grasp point.
            - contact_point_id: The index of the grasp point.
        """
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        actor_matrix = actor.get_pose().to_transformation_matrix()
        local_contact_matrix = np.asarray(actor_data['contact_points_pose'][contact_point_id])
        local_contact_matrix[:3,3] *= actor_data['scale']
        global_contact_pose_matrix = actor_matrix  @ local_contact_matrix @ np.array([[0, 0, 1, 0],
                                                                                      [-1,0, 0, 0],
                                                                                      [0, -1,0, 0],
                                                                                      [0, 0, 0, 1]])
        global_contact_pose_matrix_q = global_contact_pose_matrix[:3,:3]
        global_grasp_pose_p = global_contact_pose_matrix[:3,3] + global_contact_pose_matrix_q @ np.array([-0.12-pre_dis,0,0]).T
        global_grasp_pose_q = t3d.quaternions.mat2quat(global_contact_pose_matrix_q)
        res_pose = list(global_grasp_pose_p)+list(global_grasp_pose_q)
        # target_lst = self.robot.create_target_pose_list(res_pose,  self.get_actor_contact_point_position(actor, actor_data, contact_point_id), arm_tag)
        # TODO: z axis rotation
        # res_pose = rotate_along_axis(res_pose, self.get_actor_contact_point_position(actor, actor_data, contact_point_id), [0, 1, 0], is_random_rotate[0] + np.random.rand() * (is_random_rotate[1] - is_random_rotate[0]), axis_type='target', towards=[0, -1, 0])
        res_pose = self.choose_best_pose(res_pose, self.get_actor_contact_point_position(actor, actor_data, contact_point_id), arm_tag)
        return res_pose

    def get_grasp_pose_best_for_arm(self, actor:sapien.Entity, actor_data:dict, arm_tag:Literal['left', 'right'], z_rotation:float=0., pre_dis:float=0., pid:list[int]=None, return_type:Literal['pose', 'idx', 'both']='pose', plan_axis:np.ndarray|list=[[1,0,0]]):
        '''
            Get the contact point best for the arm.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - arm_tag: The arm to be used, either "left" or "right".
            - pid: The index of the contact point to be chosen.
            - return_type: The type of return value, either "pose", "idx", or "both", default is pose.
            - pre_dis: The distance in front of the grasp point.
            - z_rotation: The rotation angle around the z-axis, must be positive.
            - plan_axis: The axis use to calculate of plan pose, default is [[1,0,0]].
        '''
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        if 'aloha' in str(self.robot.left_urdf_path):
            arm_pose = sapien.Pose([-0.3, -0.45, 0.75], [1,0,0,0]) \
                if arm_tag == 'left' else sapien.Pose([0.3, -0.45, 0.75], [1,0,0,0])
        else:
            if arm_tag == 'left':
                arm_pose = self.robot.left_entity.get_pose()
            else:
                arm_pose = self.robot.right_entity.get_pose()

        z_rotation = np.abs(z_rotation)
        arm_vec = actor.get_pose().p - arm_pose.p
        rotate_mat = t3d.euler.euler2mat(0, 0, -z_rotation if arm_tag == 'left' else z_rotation)
        arm_vec = (rotate_mat @ arm_vec.reshape(3, 1)).reshape(3)

        return self.get_grasp_pose_closest_to_given_axis(
            actor, actor_data, arm_vec,
            pre_dis=pre_dis, pid=pid, return_type=return_type, plan_axis=plan_axis
        )


    def get_grasp_pose_closest_to_given_axis(self, actor:sapien.Entity, actor_data:dict, axis:np.ndarray|list, pre_dis:float=0., pid:list[int]=None, return_type:Literal['pose', 'idx', 'both']='pose', plan_axis:np.ndarray|list=[[1,0,0]]):
        '''
            Get the contact point closest to the given axis.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - axis: The axis to be aligned with the contact point.
            - pid: The index of the contact point to be chosen.
            - return_type: The type of return value, either "pose", "idx", or "both", default is pose.
            - pre_dis: The distance in front of the grasp point.
            - plan_axis: The axis use to calculate of plan pose, default is [[1,0,0]].
        '''
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        if pid is None: pid = range(len(actor_data.get('contact_points_pose', [])))

        def get_contact_pose(contact_point_id):
            actor_matrix = actor.get_pose().to_transformation_matrix()
            local_contact_matrix = np.asarray(actor_data['contact_points_pose'][contact_point_id])
            local_contact_matrix[:3,3] *= actor_data['scale']
            global_contact_pose_matrix = actor_matrix  @ local_contact_matrix @ np.array([[0, 0, 1, 0],
                                                                                        [-1,0, 0, 0],
                                                                                        [0, -1,0, 0],
                                                                                        [0, 0, 0, 1]])
            global_contact_pose_matrix_q = global_contact_pose_matrix[:3,:3]
            global_grasp_pose_p = global_contact_pose_matrix[:3,3] + global_contact_pose_matrix_q @ np.array([-0.12-pre_dis,0,0]).T
            global_grasp_pose_q = t3d.quaternions.mat2quat(global_contact_pose_matrix_q)
            return list(global_grasp_pose_p)+list(global_grasp_pose_q)

        axis = np.array(axis)
        chosen_pose, chosen_idx, chosen_dot = None, None, -1
        for idx in pid:
            plan_pose = get_contact_pose(idx)
            grasp_dir = t3d.quaternions.quat2mat(plan_pose[3:]) @ np.array(plan_axis).T
            grasp_dir = grasp_dir / np.linalg.norm(grasp_dir)
            grasp_dot = np.dot(grasp_dir.reshape(1, 3), axis)
            if grasp_dot > chosen_dot:
                chosen_dot, chosen_pose, chosen_idx = grasp_dot, plan_pose, idx

        if return_type == 'pose':
            return chosen_pose
        elif return_type == 'idx':
            return chosen_idx
        elif return_type == 'both':
            return chosen_pose, chosen_idx

    def get_grasp_pose_from_goal_point_and_direction(self, actor, actor_data,  endpose_tag: str, actor_functional_point_id = 0, target_point = None,
                                                     target_approach_direction = [0,0,1,0], actor_target_orientation = None, pre_dis = 0.):
        """
            Obtain the grasp pose through the given target point and contact direction.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - endpose_tag: Left and right gripper marks, with values "left" or "right".
            - actor_functional_point_id: The index of the functional point to which the object to be grasped needs to be aligned.
            - target_point: The target point coordinates for aligning the functional points of the object to be grasped.
            - target_approach_direction: The direction of the grasped object's contact target point,
                                         represented as a quaternion in the world coordinate system.
            - actor_target_orientation: The final target orientation of the object,
                                        represented as a direction vector in the world coordinate system.
            - pre_dis: The distance in front of the grasp point.
        """
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        target_approach_direction_mat = t3d.quaternions.quat2mat(target_approach_direction)
        actor_matrix = actor.get_pose().to_transformation_matrix()
        target_point_copy = deepcopy(target_point[:3])
        target_point_copy -= target_approach_direction_mat @ np.array([0,0,pre_dis])

        try:
            actor_orientation_point = np.array(actor_data['orientation_point'])[:3,3]
        except:
            actor_orientation_point = [0,0,0]

        # if actor_target_orientation is not None:
        #     actor_target_orientation = actor_target_orientation / np.linalg.norm(actor_target_orientation)

        end_effector_pose = self.robot.get_left_ee_pose() if endpose_tag == 'left' else self.robot.get_right_ee_pose()
        res_pose = None
        # res_eval= -1e10
        # for adjunction_matrix in adjunction_matrix_list:
        if actor_functional_point_id < len(actor_data['functional_matrix']):
            functional_matrix = np.asarray(actor_data['functional_matrix'][actor_functional_point_id])
        else:
            functional_matrix = deepcopy(actor_matrix)
        local_target_matrix = np.asarray(functional_matrix)
        local_target_matrix[:3,3] *= actor_data['scale']
        fuctional_matrix = actor_matrix[:3,:3] @ np.asarray(functional_matrix)[:3,:3]
        # fuctional_matrix = fuctional_matrix @ adjunction_matrix
        trans_matrix = target_approach_direction_mat @ np.linalg.inv(fuctional_matrix)
        #  @ np.array([[1,0,0],[0,-1,0],[0,0,-1]])
        ee_pose_matrix = t3d.quaternions.quat2mat(end_effector_pose[-4:])
        target_grasp_matrix = trans_matrix @ ee_pose_matrix

        res_matrix = np.eye(4)
        res_matrix[:3,3] = (actor_matrix  @ local_target_matrix)[:3,3] - end_effector_pose[:3]
        res_matrix[:3,3] = np.linalg.inv(ee_pose_matrix) @ res_matrix[:3,3]
        target_grasp_qpose = t3d.quaternions.mat2quat(target_grasp_matrix)
        # priget_grasp_pose_w_labeled_directionnt(target_grasp_matrix @ res_matrix[:3,3])
        res_pose = (target_point_copy - target_grasp_matrix @ res_matrix[:3,3]).tolist() + target_grasp_qpose.tolist()
        return res_pose

    # Get the pose coordinates of the actor's target point in the world coordinate system.
    # Return value: [x, y, z]
    def get_actor_goal_pose(self,actor,actor_data, id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        local_target_matrix = np.asarray(actor_data['target_pose'][id])
        local_target_matrix[:3,3] *= actor_data['scale']
        return (actor_matrix @ local_target_matrix)[:3,3]

    # Get the actor's functional point and axis corresponding to the index in the world coordinate system.
    # Return value: [x, y, z, quaternion].
    def get_actor_functional_pose(self, actor, actor_data, actor_functional_point_id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        # if "model_type" in actor_data.keys() and actor_data["model_type"] == "urdf": actor_matrix[:3,:3] = self.URDF_MATRIX
        local_functional_matrix = np.asarray(actor_data['functional_matrix'][actor_functional_point_id])
        local_functional_matrix[:3,3] *= actor_data['scale']
        res_matrix = actor_matrix @ local_functional_matrix
        return res_matrix[:3,3].tolist() + t3d.quaternions.mat2quat(res_matrix[:3,:3]).tolist()

    # Get the actor's grasp point and axis corresponding to the index in the world coordinate system.
    # Return value: [x, y, z, quaternion]
    def get_actor_contact_point_position(self, actor, actor_data, actor_contact_id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        # if "model_type" in actor_data.keys() and actor_data["model_type"] == "urdf": actor_matrix[:3,:3] = self.URDF_MATRIX
        local_contact_matrix = np.asarray(actor_data['contact_points_pose'][actor_contact_id])
        local_contact_matrix[:3,3] *= actor_data['scale']
        res_matrix = actor_matrix @ local_contact_matrix
        return res_matrix[:3,3].tolist() + t3d.quaternions.mat2quat(res_matrix[:3,:3]).tolist()

    def _reward_grasp_pose(self, grasp_pose):
        pass

    def _default_choose_grasp_pose(self, actor, actor_data, arm_tag, pre_dis):
        """
            Default grasp pose function.
            - actor: The target actor to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - arm_tag: The arm to be used for grasping, default is None, which use the closed gripper, either "left" or "right".
            - pre_dis: The distance in front of the grasp point, default is 0.1.
        """
        id = -1
        score = -1
        for i in range(len(actor_data['contact_points_pose'])):
            contact_point = self.get_actor_contact_point_position(actor, actor_data, i)
            pose = self.get_grasp_pose_w_labeled_direction(actor, actor_data, pre_dis, i)
            now_score = 0
            if not (contact_point[1] < -0.1 and pose[2] < 0.85 or contact_point[1] > 0.05 and pose[2] > 0.92):
                now_score -= 1
            quat_dis = cal_quat_dis(pose[-4:], GRASP_DIRECTION_DIC[arm_tag + '_arm_perf'])

        return self.get_grasp_pose_w_labeled_direction(actor, actor_data, pre_dis=pre_dis)

    def _test_choose_grasp_pose(self, actor, actor_data, arm_tag, pre_dis, target_dis = 0, direc = None):
        """
            Test the grasp pose function.
            - actor: The target actor to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - arm_tag: The arm to be used for grasping, default is None, which use the closed gripper, either "left" or "right".
            - pre_dis: The distance in front of the grasp point, default is 0.1.
        """
        if not self.plan_success:
            return
        res_pre_top_down_pose = None
        res_top_down_pose = None
        dis_top_down = 1e9
        res_pre_side_pose = None
        res_side_pose = None
        dis_side = 1e9
        res_pre_pose = None
        res_pose = None
        dis = 1e9

        pref_direction = self.robot.get_grasp_perfect_direction(arm_tag)

        def get_grasp_pose(pre_grasp_pose, pre_grasp_dis):
            grasp_pose = deepcopy(pre_grasp_pose)
            grasp_pose = np.array(grasp_pose)
            direction_mat = t3d.quaternions.quat2mat(grasp_pose[-4:])
            grasp_pose[:3] += [pre_grasp_dis, 0, 0] @ np.linalg.inv(direction_mat)
            grasp_pose = grasp_pose.tolist()
            return grasp_pose
            # res = self.robot.left_plan_path(grasp_pose)
            # return res['status'] == 'Success'

        def check_pose(pre_pose, pose, arm_tag):
            if arm_tag == 'left':
                plan_func = self.robot.left_plan_path
            else:
                plan_func = self.robot.right_plan_path
            pre_path = plan_func(pre_pose)
            if pre_path['status'] != 'Success':
                return False
            pre_qpos = pre_path['position'][-1]
            # return plan_func(pose, constraint_pose=[1,1,1,0,1,1], last_qpos=pre_qpos)['status'] == 'Success'
            # return plan_func(pose, constraint_pose=[1,1,1,0,0,1], last_qpos=pre_qpos)['status'] == 'Success'
            # return plan_func(pose, constraint_pose=[1,1,1,0,0,0], last_qpos=pre_qpos)['status'] == 'Success'
            return plan_func(pose)['status'] == 'Success'

        for i in range(0, len(actor_data['contact_points_pose'])):
            pre_pose = self.get_grasp_pose_w_labeled_direction(actor, actor_data, pre_dis, contact_point_id=i, arm_tag=arm_tag)

            if pre_pose is None:
                continue
            pose = get_grasp_pose(pre_pose, pre_dis - target_dis)
            # if not check_pose(pre_pose, pose, arm_tag):
            #     continue
            now_dis_top_down = cal_quat_dis(pose[-4:], GRASP_DIRECTION_DIC['top_down_little_left' if arm_tag == 'right' else 'top_down_little_right'])
            now_dis_side = cal_quat_dis(pose[-4:], GRASP_DIRECTION_DIC[pref_direction])
            # add_robot_visual_box(self, pre_pose, f'id{i}_{now_dis_top_down:.3f}_{now_dis_side:.3f}')
            if (res_pre_top_down_pose is None or now_dis_top_down < dis_top_down):
                res_pre_top_down_pose = pre_pose
                res_top_down_pose = pose
                dis_top_down = now_dis_top_down

            if res_pre_side_pose is None or now_dis_side < dis_side:
            # if res_pre_side_pose is None or cal_quat_dis(pre_pose[-4:], GRASP_DIRECTION_DIC['front']) < sum_side:
                res_pre_side_pose = pre_pose
                res_side_pose = pose
                dis_side = now_dis_side
                # sum_side = cal_quat_dis(pre_pose[-4:], GRASP_DIRECTION_DIC['front'])

            now_dis = 0.7 * now_dis_top_down + 0.3 * now_dis_side
            if res_pre_pose is None or now_dis < dis:
                res_pre_pose = pre_pose
                res_pose = pose
                dis = now_dis

        # print(dis_top_down)
        # print(dis_side)
        # TODO
        if direc == 'top_down':
            return res_pre_top_down_pose, res_top_down_pose
        if direc == 'side':
            return res_pre_side_pose, res_side_pose
        if dis_top_down < 0.15:
            return res_pre_top_down_pose, res_top_down_pose
        if dis_side < 0.15:
            return res_pre_side_pose, res_side_pose
        return res_pre_pose, res_pose
        # return res_pre_side_pose, res_side_pose
        # if sum_top_down < sum_side:
        #     return res_pre_top_down_pose, res_top_down_pose
        # return res_pre_side_pose, res_side_pose

    def _test_grasp_single_actor(self, actor, actor_data, arm_tag = None, pre_grasp_dis = 0.1, grasp_dis = 0, gripper_pos = 0.15, direc = None):
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]
        pre_grasp_pose, grasp_pose = self._test_choose_grasp_pose(actor, actor_data, pre_dis=pre_grasp_dis, arm_tag=arm_tag, target_dis = grasp_dis, direc=direc)
        move_func = self.left_move_to_pose if arm_tag == 'left' else self.right_move_to_pose
        close_func = self.close_left_gripper if arm_tag == 'left' else self.close_right_gripper
        # add_robot_visual_box(self, pre_grasp_pose)
        move_func(pre_grasp_pose)
        # pause(self)
        # TODO
        # move_func(grasp_pose, constraint_pose=[1,1,1,0,1,1])
        # move_func(grasp_pose, constraint_pose=[1,1,1,0,0,1])
        move_func(grasp_pose, constraint_pose=[1,1,1,0,0,0])
        close_func(pos = gripper_pos)
        return grasp_pose

    def _test_grasp_dual_actor(self, left_actor, left_actor_data, right_actor, right_actor_data, left_pre_grasp_dis = 0.1, right_pre_grasp_dis = 0.1,
                               left_grasp_dis = 0, right_grasp_dis = 0, left_gripper_pos = 0.15, right_gripper_pos = 0.15, left_direc = None, right_direc = None):
        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1] and [-1, -1, -1, -1, -1, -1, -1]
        left_pre_grasp_pose, left_grasp_pose = self._test_choose_grasp_pose(left_actor, left_actor_data, pre_dis=left_pre_grasp_dis,
                                                                            arm_tag='left', target_dis=left_grasp_dis, direc=left_direc)
        right_pre_grasp_pose, right_grasp_pose = self._test_choose_grasp_pose(right_actor, right_actor_data, pre_dis=right_pre_grasp_dis,
                                                                              arm_tag='right', target_dis=right_grasp_dis, direc=right_direc)
        # pause(self)
        self.together_move_to_pose(left_pre_grasp_pose, right_pre_grasp_pose)
        # TODO
        # self.together_move_to_pose(left_grasp_pose, right_grasp_pose, left_constraint_pose=[1,1,1,0,1,1], right_constraint_pose=[1,1,1,0,1,1])
        # self.together_move_to_pose(left_grasp_pose, right_grasp_pose, left_constraint_pose=[1,1,1,0,0,1], right_constraint_pose=[1,1,1,0,0,1])
        self.together_move_to_pose(left_grasp_pose, right_grasp_pose, left_constraint_pose=[1,1,1,0,0,0], right_constraint_pose=[1,1,1,0,0,0])
        # move_func(grasp_pose, constraint_pose=[1,1,1,0,0,0])
        self.together_close_gripper(left_pos = left_gripper_pos, right_pos = right_gripper_pos)
        return left_grasp_pose, right_grasp_pose

    def _test_get_place_pose(
        self, actor:sapien.Entity, actor_data:dict, target_pose:list|np.ndarray,
        arm_tag:Literal['left', 'right'], constrain:Literal['free', 'align', 'auto']='auto',
        align_axis: list[np.ndarray] | np.ndarray | list = None,
        actor_axis: np.ndarray | list = [1,0,0],
        actor_axis_type: Literal['actor', 'world'] = 'actor',
        functional_point_id:int = None, pre_dis:float = 0.1,
        pre_dis_axis:Literal['grasp', 'fp']|np.ndarray|list='grasp'):

        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]

        actor_matrix = actor.get_pose().to_transformation_matrix()
        if functional_point_id is not None:
            place_start_pose = transforms._toPose(self.get_actor_functional_pose(
                actor, actor_data, functional_point_id))
            z_transform = False
        else:
            place_start_pose = actor.get_pose()
            z_transform = True

        end_effector_pose = self.robot.get_left_ee_pose() \
            if arm_tag == 'left' else self.robot.get_right_ee_pose()

        if constrain == 'auto':
            grasp_direct_vec = place_start_pose.p - end_effector_pose[:3]
            if np.abs(np.dot(grasp_direct_vec, [0, 0, 1])) <= 0.1:
                # 侧抓，则根据左右夹爪自动选择抓取方向
                place_pose = get_place_pose(
                    place_start_pose, target_pose, constrain='align',
                    actor_axis=grasp_direct_vec, actor_axis_type='world',
                    align_axis=[1, 1, 0] if arm_tag == 'left' else [-1, 1, 0], z_transform=z_transform
                )
            else:
                # 俯抓，维持相机向前
                camera_vec = transforms._toPose(end_effector_pose).\
                    to_transformation_matrix()[:3, 2]
                place_pose = get_place_pose(
                    place_start_pose, target_pose, constrain='align',
                    actor_axis=camera_vec, actor_axis_type='world',
                    align_axis=[0, 1, 0], z_transform=z_transform
                )
        else:
            place_pose = get_place_pose(
                place_start_pose, target_pose, constrain=constrain,
                actor_axis=actor_axis, actor_axis_type=actor_axis_type,
                align_axis=align_axis, z_transform=z_transform
            )
        start2target = transforms._toPose(place_pose).to_transformation_matrix()[:3, :3] @ \
            place_start_pose.to_transformation_matrix()[:3, :3].T
        target_point = (
            start2target @ (actor_matrix[:3, 3] - place_start_pose.p).reshape(3, 1)
        ).reshape(3) + np.array(place_pose[:3])

        ee_pose_matrix = t3d.quaternions.quat2mat(end_effector_pose[-4:])
        target_grasp_matrix = start2target @ ee_pose_matrix

        res_matrix = np.eye(4)
        res_matrix[:3,3] = actor_matrix[:3,3] - end_effector_pose[:3]
        res_matrix[:3,3] = np.linalg.inv(ee_pose_matrix) @ res_matrix[:3,3]
        target_grasp_qpose = t3d.quaternions.mat2quat(target_grasp_matrix)

        grasp_bias = target_grasp_matrix @ res_matrix[:3, 3]
        if pre_dis_axis == 'grasp':
            target_dis_vec = target_grasp_matrix @ res_matrix[:3, 3]
            target_dis_vec /= np.linalg.norm(target_dis_vec)
        else:
            target_pose_mat = transforms._toPose(target_pose).to_transformation_matrix()
            if pre_dis_axis == 'fp':
                pre_dis_axis = [0., 0., 1.]
            pre_dis_axis = np.array(pre_dis_axis)
            pre_dis_axis /= np.linalg.norm(pre_dis_axis)
            target_dis_vec = (target_pose_mat[:3, :3] @ \
                np.array(pre_dis_axis).reshape(3, 1)).reshape(3)
            target_dis_vec /= np.linalg.norm(target_dis_vec)
        res_pose = (target_point - grasp_bias - pre_dis*target_dis_vec).tolist() \
            + target_grasp_qpose.tolist()
        return res_pose

    def _test_place(self, actor:sapien.Entity, actor_data:dict, target_pose:list|np.ndarray,
        arm_tag:Literal['left', 'right'], functional_point_id:int = None,
        pre_dis:float = 0.1, dis:float = 0.02, is_open:bool = True, **args):

        if not self.plan_success:
            return [-1, -1, -1, -1, -1, -1, -1]

        place_pre_pose = self._test_get_place_pose(
            actor, actor_data, target_pose, arm_tag,
            functional_point_id=functional_point_id, pre_dis=pre_dis,
            **args
        )
        place_pose = self._test_get_place_pose(
            actor, actor_data, target_pose, arm_tag,
            functional_point_id=functional_point_id, pre_dis=dis,
            **args
        )
        move_func = self.left_move_to_pose if arm_tag == 'left' else self.right_move_to_pose
        open_func = self.open_left_gripper if arm_tag == 'left' else self.open_right_gripper

        move_func(place_pre_pose)
        move_func(place_pose)
        if is_open:
            open_func()
        return place_pose

    # =========================================================== End New APIS ===========================================================
    def add_prohibit_area(self, actor:sapien.Entity|sapien.Pose|list|np.ndarray,
                          actor_data:dict={}, padding=0.01):
        if isinstance(actor, sapien.Pose) \
            or isinstance(actor, list) \
            or isinstance(actor, np.ndarray):
            actor_pose = transforms._toPose(actor)
        else:
            actor_pose = actor.get_pose()

        scale:float = actor_data.get('scale', 1)
        origin_bounding_size = np.array(
            actor_data.get('extents', [0.1, 0.1, 0.1])) * scale / 2
        origin_bounding_pts = np.array([
            [-1, -1, -1], [-1, -1, 1], [-1, 1, -1], [-1, 1, 1],
            [1, -1, -1], [1, -1, 1], [1, 1, -1], [1, 1, 1]
        ]) * origin_bounding_size

        actor_matrix = actor_pose.to_transformation_matrix()
        trans_bounding_pts = actor_matrix[:3, :3] @ origin_bounding_pts.T + actor_matrix[:3, 3].reshape(3, 1)
        x_min = np.min(trans_bounding_pts[0]) - padding
        x_max = np.max(trans_bounding_pts[0]) + padding
        y_min = np.min(trans_bounding_pts[1]) - padding
        y_max = np.max(trans_bounding_pts[1]) + padding
        # add_robot_visual_box(self, [x_min, y_min, actor_matrix[3, 3]])
        # add_robot_visual_box(self, [x_max, y_max, actor_matrix[3, 3]])
        self.prohibited_area.append([x_min, y_min, x_max, y_max])

    def _take_picture(self): # Save data
        if not self.is_save:
            return
        print('saving: episode = ', self.ep_num, ' index = ',self.PCD_INDEX, end='\r')

        if self.PCD_INDEX==0:
            self.folder_path ={
                "pkl" : f"{self.save_dir}/episode{self.ep_num}/",
                "cache": f"{self.save_dir}/.cache/episode{self.ep_num}/"
            }

            for directory in self.folder_path.values():
                if os.path.exists(directory):
                    file_list = os.listdir(directory)
                    for file in file_list:
                        os.remove(directory + file)
        pkl_dic = self.get_obs(is_policy=False)
        # save_pkl(self.folder_path["pkl"]+f"{self.PCD_INDEX}.pkl", pkl_dic)
        save_pkl(self.folder_path["cache"]+f"{self.PCD_INDEX}.pkl", pkl_dic) # use cache
        self.PCD_INDEX += 1

    def save_traj_data(self, idx):
        folder_path = f"{self.save_dir}/_traj_data"
        file_path = os.path.join(folder_path, f'episode{idx}.pkl')
        traj_data = {"left_path_lst": deepcopy(self.left_path_lst), "right_path_lst": deepcopy(self.right_path_lst)}
        save_pkl(file_path, traj_data)

    def load_tran_data(self, idx):
        assert self.save_dir is not None, "self.save_dir is None"
        folder_path = os.path.join(self.save_dir, "_traj_data")
        file_path = os.path.join(folder_path, f'episode{idx}.pkl')
        with open(file_path, 'rb') as f:
            traj_data = pickle.load(f)
        return traj_data

    def merge_pkl_to_hdf5_video(self):
        if not self.is_save:
            return
        cache_path = self.folder_path["cache"]
        target_file_path = f"{self.save_dir}/episode{self.ep_num}.hdf5"
        target_video_path = f"{self.save_dir}/video/episode{self.ep_num}.mp4"
        # print('Merging pkl to hdf5: ', cache_path, ' -> ', target_file_path)

        process_folder_to_hdf5_video(cache_path, target_file_path, target_video_path)

    def remove_cache(self):
        folder_path = self.folder_path['cache']
        GREEN = "\033[92m"
        RED = "\033[91m"
        RESET = "\033[0m"
        try:
            shutil.rmtree(folder_path)
            print(f"{GREEN}Folder {folder_path} deleted successfully.{RESET}")
        except OSError as e:
            print(f"{RED}Error: {folder_path} is not empty or does not exist.{RESET}")

    def set_instruction(self, instruction=None):
        self.instruction = instruction

    def get_instruction(self, instruction=None):
        return self.instruction

    def get_obs(self, is_policy = True):
        self._update_render()
        self.cameras.update_picture()
        pkl_dic = {
            "observation":{
                # "left_camera":{},
                # "right_camera":{},
                # "head_camera":{},   # rbg , mesh_seg , actior_seg , depth , intrinsic_cv , extrinsic_cv , cam2world_gl(model_matrix)
                # "front_camera":{}
            },
            "pointcloud":[],   # conbinet pcd
            "joint_action":{},
            "endpose":[]
        }

        pkl_dic['observation'] = self.cameras.get_config()
        # # ---------------------------------------------------------------------------- #
        # # RGBA
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('rgb', False):
            rgba = self.cameras.get_rgba()
            for camera_name in rgba.keys():
                pkl_dic['observation'][camera_name].update(rgba[camera_name])

        if self.data_type.get('observer', False):
            observer = self.cameras.get_observer_rgba()
            pkl_dic['observer_rgb'] = observer
        # # ---------------------------------------------------------------------------- #
        # # mesh_segmentation
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('mesh_segmentation', False):
            mesh_segmentation = self.cameras.get_segmentation(level='mesh')
            for camera_name in mesh_segmentation.keys():
                pkl_dic['observation'][camera_name].update(mesh_segmentation[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # actor_segmentation
        # # --------------------------------------------------------------------------- #
        if self.data_type.get('actor_segmentation', False):
            actor_segmentation = self.cameras.get_segmentation(level='actor')
            for camera_name in actor_segmentation.keys():
                pkl_dic['observation'][camera_name].update(actor_segmentation[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # DEPTH
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('depth', False):
            depth = self.cameras.get_depth()
            for camera_name in depth.keys():
                pkl_dic['observation'][camera_name].update(depth[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # endpose JSON
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('endpose', False):

            def trans_endpose_quat2rpy(endpose, gripper_val):
                rpy = t3d.euler.quat2euler(endpose[-4:])
                roll, pitch, yaw = rpy
                x,y,z = endpose[:3]
                endpose = {
                    "gripper": float(gripper_val),
                    "pitch" : float(pitch),
                    "roll" : float(roll),
                    "x": float(x),
                    "y": float(y),
                    "yaw" : float(yaw),
                    "z": float(z),
                }
                return endpose

            # TODO
            norm_gripper_val = [self.robot.get_left_gripper_val(), self.robot.get_right_gripper_val()]
            left_endpose = trans_endpose_quat2rpy(self.robot.get_left_endpose(), norm_gripper_val[0])
            right_endpose = trans_endpose_quat2rpy(self.robot.get_right_endpose(), norm_gripper_val[1])

            # tmp
            # left_endpose = trans_endpose_quat2rpy(self.robot.get_left_orig_endpose(), norm_gripper_val[0])
            # right_endpose = trans_endpose_quat2rpy(self.robot.get_right_orig_endpose(), norm_gripper_val[1])

            if self.dual_arm:
                pkl_dic["endpose"] = np.array([left_endpose["x"],left_endpose["y"],left_endpose["z"],left_endpose["roll"],
                                            left_endpose["pitch"],left_endpose["yaw"],left_endpose["gripper"],
                                            right_endpose["x"],right_endpose["y"],right_endpose["z"],right_endpose["roll"],
                                            right_endpose["pitch"],right_endpose["yaw"],right_endpose["gripper"],])
            else:
                pkl_dic["endpose"] = np.array([right_endpose["x"],right_endpose["y"],right_endpose["z"],right_endpose["roll"],
                                                right_endpose["pitch"],right_endpose["yaw"],right_endpose["gripper"],])

            # tmp
            # pkl_dic["endpose"] = self.robot.get_left_orig_endpose()
        # # ---------------------------------------------------------------------------- #
        # # JointState JSON
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('qpos', False):

            left_jointstate = self.robot.get_left_arm_jointState()
            right_jointstate = self.robot.get_right_arm_jointState()

            #tmp
            # left_jointstate = self.robot.get_left_arm_real_jointState()
            # right_jointstate = self.robot.get_right_arm_real_jointState()

            if self.dual_arm:
                # pkl_dic["joint_action"] = np.array(left_jointstate+right_jointstate)
                pkl_dic['joint_action']['left_arm'] = left_jointstate[:-1]
                pkl_dic['joint_action']['left_gripper'] = left_jointstate[-1]
                pkl_dic['joint_action']['right_arm'] = right_jointstate[:-1]
                pkl_dic['joint_action']['right_gripper'] = right_jointstate[-1]
                pkl_dic['joint_action']['vector'] = np.array(left_jointstate + right_jointstate)
            else:
                # pkl_dic["joint_action"] = np.array(right_jointstate)
                pkl_dic['right_arm'] = right_jointstate[:-1]
                pkl_dic['joint_action']['right_gripper'] = right_jointstate[-1]


        # # ---------------------------------------------------------------------------- #
        # # PointCloud
        # # ---------------------------------------------------------------------------- #
        # if self.data_type.get('pointcloud', False):
        #     pkl_dic["pointcloud"] = self.cameras.get_pcd(self.data_type.get("conbine", False))
        #===========================================================#
        self.now_obs = deepcopy(pkl_dic)
        return pkl_dic

    def get_cam_obs(self, observation: dict) -> dict:
        head_cam = np.moveaxis(observation['observation']['head_camera']['rgb'], -1, 0) / 255
        # front_cam = np.moveaxis(observation['observation']['front_camera']['rgb'], -1, 0) / 255
        left_cam = np.moveaxis(observation['observation']['left_camera']['rgb'], -1, 0) / 255
        right_cam = np.moveaxis(observation['observation']['right_camera']['rgb'], -1, 0) / 255
        return dict(
            head_cam = head_cam,
            # front_cam = front_cam,
            left_cam = left_cam,
            right_cam = right_cam
        )

    def get_messy_table(self, messy_numbers=10, xlim=[-0.59,0.59], ylim=[-0.34,0.34], zlim=[0.741]):
        self.record_messy_objects = [] # record messy objects

        xlim[0] += self.table_bias[0]
        xlim[1] += self.table_bias[0]
        ylim[0] += self.table_bias[1]
        ylim[1] += self.table_bias[1]

        if np.random.rand() < self.clean_background_rate:
            return

        task_objects_list = []
        for entity in self.scene.get_all_actors():
            actor_name = entity.get_name()
            if actor_name == '': continue
            if actor_name in ['table', 'wall', 'ground']: continue
            task_objects_list.append(actor_name)
        self.obj_names, self.messy_item_info = get_available_messy_objects(task_objects_list)

        success_count = 0
        max_try = 50
        trys = 0
        while success_count < messy_numbers and trys < max_try:
            obj = np.random.randint(len(self.obj_names))
            obj_name = self.obj_names[obj]
            obj_idx = np.random.randint(len(self.messy_item_info[obj_name]['ids']))
            obj_idx = self.messy_item_info[obj_name]['ids'][obj_idx]
            obj_radius = self.messy_item_info[obj_name]['params'][obj_idx]['radius']
            obj_offset = self.messy_item_info[obj_name]['params'][obj_idx]['z_offset']
            obj_maxz = self.messy_item_info[obj_name]['params'][obj_idx]['z_max']

            success, self.messy_obj = rand_create_messy_actor(
                self.scene,
                xlim=xlim,
                ylim=ylim,
                zlim=np.array(zlim)+self.bias,
                modelname=obj_name,
                modelid  =obj_idx,
                modeltype=self.messy_item_info[obj_name]['type'],
                rotate_rand=True,
                rotate_lim=[0,0,math.pi],
                size_dict=self.size_dict,
                obj_radius=obj_radius,
                z_offset=obj_offset,
                z_max=obj_maxz,
                prohibited_area=self.prohibited_area,
            )
            if not success:
                trys += 1
                continue
            self.messy_obj.set_name(f'{obj_name}')

            self.messy_objs.append(self.messy_obj)
            pose = self.messy_obj.get_pose().p.tolist()
            pose.append(obj_radius)
            self.size_dict.append(pose)
            success_count += 1
            self.record_messy_objects.append({"object_type": obj_name, "object_index": obj_idx})

        if success_count < messy_numbers:
            print(f"Warning: Only {success_count} messy objects are placed on the table.")

        self.size_dict = None
        self.messy_objs = []

    def apply_policy(self, model, update_func, res_save_dir, args):
        cnt = 0
        self.test_num += 1

        eval_video_log = args['eval_video_log']
        video_size = str(args['head_camera_w']) + 'x' + str(args['head_camera_h'])

        if eval_video_log:
            import subprocess
            from pathlib import Path
            res_save_dir = Path('eval_video') / res_save_dir
            res_save_dir.mkdir(parents=True, exist_ok=True)
            ffmpeg = subprocess.Popen([
                'ffmpeg', '-y', '-loglevel', 'error',
                '-f', 'rawvideo',
                '-pixel_format', 'rgb24',
                '-video_size', video_size,
                '-framerate', '10',
                '-i', '-',
                '-pix_fmt', 'yuv420p',
                '-vcodec', 'libx264',
                '-crf', '23',
                f'{res_save_dir}/{self.test_num}.mp4'
            ], stdin=subprocess.PIPE)

        success_flag = False
        self._update_render()
        if self.render_freq:
            if self.viewer: self.viewer.render()

        self.actor_pose = True
        observation = self.get_obs()
        if eval_video_log:
            ffmpeg.stdin.write(observation['observation']['head_camera']['rgb'].tobytes())

        while cnt < self.step_lim:
            observation = self.get_obs()
            obs = update_func(observation)
            model.update_obs(obs)
            take_actions = model.get_action()
            # obs = model.get_last_obs()
            for action in take_actions:
                actions = np.array([action])
                left_jointstate = self.robot.get_left_arm_jointState()
                right_jointstate = self.robot.get_right_arm_jointState()
                current_jointstate = np.array(left_jointstate + right_jointstate)

                left_arm_actions , left_gripper_actions , left_current_qpos, left_path = [], [], [], []
                right_arm_actions , right_gripper_actions , right_current_qpos, right_path = [], [], [], []
                if self.dual_arm:
                    left_arm_actions,left_gripper_actions = actions[:, :6],actions[:, 6]
                    right_arm_actions,right_gripper_actions = actions[:, 7:13],actions[:, 13]
                    left_current_qpos, right_current_qpos = current_jointstate[:6], current_jointstate[7:13]
                    left_current_gripper, right_current_gripper = current_jointstate[6:7], current_jointstate[13:14]
                else:
                    right_arm_actions,right_gripper_actions = actions[:, :6],actions[:, 6]
                    right_current_qpos = current_jointstate[:6]
                    right_current_gripper = current_jointstate[6:7]

                if self.dual_arm:
                    left_path = np.vstack((left_current_qpos, left_arm_actions))
                    left_gripper_path = np.hstack((left_current_gripper, left_gripper_actions))
                right_path = np.vstack((right_current_qpos, right_arm_actions))
                right_gripper_path = np.hstack((right_current_gripper, right_gripper_actions))

                try:
                    times, left_pos, left_vel, acc, duration = self.robot.left_planner.TOPP(left_path, 1/250, verbose=True)
                    left_result = dict()
                    left_result['position'], left_result['velocity'] = left_pos, left_vel
                    left_n_step = left_result["position"].shape[0]
                    # left_gripper = np.linspace(left_gripper[0], left_gripper[-1], left_n_step)
                except Exception as e:
                    print('left arm TOPP error: ', e)
                    topp_left_flag = False
                    left_n_step = 1

                if left_n_step == 0 or (not self.dual_arm):
                    topp_left_flag = False
                    left_n_step = 1

                try:
                    times, right_pos, right_vel, acc, duration = self.robot.right_planner.TOPP(right_path, 1/250, verbose=True)
                    right_result = dict()
                    right_result['position'], right_result['velocity'] = right_pos, right_vel
                    right_n_step = right_result["position"].shape[0]
                    # right_gripper = np.linspace(right_gripper[0], right_gripper[-1], right_n_step)
                except Exception as e:
                    print('right arm TOPP error: ', e)
                    topp_right_flag = False
                    right_n_step = 1

                if right_n_step == 0:
                    topp_right_flag = False
                    right_n_step = 1

                n_step = max(left_n_step, right_n_step)

                obs_update_freq = n_step // actions.shape[0]

                # Calculate gripper step path
                left_mod_num = left_n_step % len(left_gripper_actions)
                right_mod_num = right_n_step % len(right_gripper_actions)
                left_gripper_step = [0] + [left_n_step // len(left_gripper_actions) + (1 if i < left_mod_num else 0) for i in range(len(left_gripper_actions))]
                right_gripper_step = [0] + [right_n_step // len(right_gripper_actions) + (1 if i < right_mod_num else 0) for i in range(len(right_gripper_actions))]

                left_gripper = []
                for gripper_step in range(1, left_gripper_path.shape[0]):
                    region_left_gripper = np.linspace(left_gripper_path[gripper_step-1], left_gripper_path[gripper_step], left_gripper_step[gripper_step]+1)[1:]
                    left_gripper = left_gripper + region_left_gripper.tolist()
                left_gripper = np.array(left_gripper)

                right_gripper = []
                for gripper_step in range(1, right_gripper_path.shape[0]):
                    region_right_gripper = np.linspace(right_gripper_path[gripper_step-1], right_gripper_path[gripper_step], right_gripper_step[gripper_step]+1)[1:]
                    right_gripper = right_gripper + region_right_gripper.tolist()
                right_gripper = np.array(right_gripper)

                now_left_id = 0 if topp_left_flag else 1e9
                now_right_id = 0 if topp_right_flag else 1e9

                i = 0

                while now_left_id < left_n_step or now_right_id < right_n_step:
                    if topp_left_flag and now_left_id < left_n_step and now_left_id / left_n_step <= now_right_id / right_n_step:
                        self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id],'left')
                        if not self.fix_gripper:
                            self.robot.set_gripper(left_gripper[now_left_id], 'left')

                        now_left_id +=1

                    if topp_right_flag and now_right_id < right_n_step and now_right_id / right_n_step <= now_left_id / left_n_step:
                        self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id],'right')
                        if not self.fix_gripper:
                            self.robot.set_gripper(right_gripper[now_right_id], 'right')

                        now_right_id +=1

                    self.scene.step()
                    self._update_render()

                    # if i != 0 and i % obs_update_freq == 0:
                    #     observation = self.get_obs()
                    #     obs = update_func(observation)
                    #     model.update_obs(obs)
                    #     self._take_picture()

                    # if self.render_freq and i % self.render_freq == 0:
                    #     self._update_render()
                    #     if self.viewer: self.viewer.render()

                    i+=1
                    if self.check_success():
                        success_flag = True
                        break

                    if self.actor_pose == False:
                        break

                self. _update_render()
                observation = self.get_obs()
                obs = update_func(observation)
                model.update_obs(obs)

                if self.render_freq:
                    if self.viewer: self.viewer.render()

                if eval_video_log:
                    ffmpeg.stdin.write(observation['observation']['head_camera']['rgb'].tobytes())

                cnt += 1
                print(f'step: {cnt} / {self.step_lim}', end='\r')

                if success_flag:
                    print("\nsuccess!")
                    self.suc +=1

                    if eval_video_log:
                        ffmpeg.stdin.close()
                        ffmpeg.wait()
                        del ffmpeg

                    return

                if self.actor_pose == False:
                    break

                if cnt == self.step_lim:
                    break

        print("\nfail!")

        if eval_video_log:
            ffmpeg.stdin.close()
            ffmpeg.wait()
            del ffmpeg

    def apply_dp3(self, model, args): # TODO
        self.test_num += 1

        step_cnt = 0

        eval_video_log = args['eval_video_log']
        camera_config = get_camera_config(str(args['camera']['head_camera_type']))
        video_size = str(camera_config['w']) + 'x' + str(camera_config['h'])
        save_dir = 'DP3/' + str(args['task_name']) + '_' + str(args['setting']) + '/' + str(args['checkpoint_id']) + '_seed' + str(args['eval_seed'])

        if eval_video_log:
            import subprocess
            from pathlib import Path
            save_dir = Path('eval_video') / save_dir
            save_dir.mkdir(parents=True, exist_ok=True)
            ffmpeg = subprocess.Popen([
                'ffmpeg', '-y', '-loglevel', 'error',
                '-f', 'rawvideo',
                '-pixel_format', 'rgb24',
                '-video_size', video_size,
                '-framerate', '10',
                '-i', '-',
                '-pix_fmt', 'yuv420p',
                '-vcodec', 'libx264',
                '-crf', '23',
                f'{save_dir}/{self.test_num}.mp4'
            ], stdin=subprocess.PIPE)

        success_flag = False
        self._update_render()
        if self.render_freq:
            if self.viewer: self.viewer.render()

        self.actor_pose = True

        observation = self.get_obs()
        if eval_video_log:
            ffmpeg.stdin.write(observation['observation']['head_camera']['rgb'].tobytes())

        while step_cnt < self.step_lim:
            observation = self.get_obs()
            obs = dict()
            obs['point_cloud'] = observation['pointcloud']
            if self.dual_arm:
                obs['agent_pos'] = observation['joint_action']
                assert obs['agent_pos'].shape[0] == 14, 'agent_pose shape, error'
            else:
                obs['agent_pos'] = observation['joint_action']
                assert obs['agent_pos'].shape[0] == 7, 'agent_pose shape, error'

            if step_cnt == 0:
                model.update_obs(obs)

            pred_actions = model.get_action(None)
            take_actions = pred_actions[:]

            for action in take_actions:
                actions = np.array([action])

                left_jointstate = self.robot.get_left_arm_jointState()
                right_jointstate = self.robot.get_right_arm_jointState()
                current_jointstate = np.array(left_jointstate + right_jointstate)
                left_arm_actions , left_gripper_actions , left_current_qpos, left_path = [], [], [], []
                right_arm_actions, right_gripper_actions, right_current_qpos, right_path = [], [], [], []

                left_arm_dim = observation['joint_action']['left_arm'].shape[0]
                right_arm_dim = observation['joint_action']['right_arm'].shape[0]
                left_arm_actions, left_gripper_actions = actions[:, :left_arm_dim], actions[:, left_arm_dim]
                right_arm_actions, right_gripper_actions = actions[:, left_arm_dim+1:left_arm_dim+right_arm_dim+1], actions[:, left_arm_dim+right_arm_dim+1]

                left_current_qpos, right_current_qpos = current_jointstate[:left_arm_dim], current_jointstate[left_arm_dim+1:left_arm_dim+right_arm_dim+1]
                left_current_gripper, right_current_gripper = current_jointstate[left_arm_dim:left_arm_dim+1], current_jointstate[left_arm_dim+right_arm_dim+1:left_arm_dim+right_arm_dim+2]

                left_path = np.vstack((left_current_qpos, left_arm_actions))
                left_gripper_path = np.hstack((left_current_gripper, left_gripper_actions))

                right_path = np.vstack((right_current_qpos, right_arm_actions))
                right_gripper_path = np.hstack((right_current_gripper, right_gripper_actions))

                topp_left_flag, topp_right_flag = True, True

                topp_left_flag, topp_right_flag = True, True
                print(left_path)
                print(type(left_path))
                print(left_path.shape)
                try:
                    times, left_pos, left_vel, acc, duration = self.robot.left_planner.planner.TOPP(left_path, 1/250, verbose=True)
                    left_result = dict()
                    left_result['position'], left_result['velocity'] = left_pos, left_vel
                    left_n_step = left_result["position"].shape[0]
                except Exception as e:
                    print('left arm TOPP error: ', e)
                    topp_left_flag = False
                    left_n_step = 1

                if left_n_step == 0:
                    topp_left_flag = False
                    left_n_step = 1

                try:
                    times, right_pos, right_vel, acc, duration = self.robot.right_planner.planner.TOPP(right_path, 1/250, verbose=True)
                    right_result = dict()
                    right_result['position'], right_result['velocity'] = right_pos, right_vel
                    right_n_step = right_result["position"].shape[0]
                except Exception as e:
                    print('right arm TOPP error: ', e)
                    topp_right_flag = False
                    right_n_step = 1

                if right_n_step == 0:
                    topp_right_flag = False
                    right_n_step = 1

                n_step = max(left_n_step, right_n_step)

                obs_update_freq = n_step // actions.shape[0]

                left_mod_num = left_n_step % len(left_gripper_actions)
                right_mod_num = right_n_step % len(right_gripper_actions)
                left_gripper_step = [0] + [left_n_step // len(left_gripper_actions) + (1 if i < left_mod_num else 0) for i in range(len(left_gripper_actions))]
                right_gripper_step = [0] + [right_n_step // len(right_gripper_actions) + (1 if i < right_mod_num else 0) for i in range(len(right_gripper_actions))]

                left_gripper = []
                for gripper_step in range(1, left_gripper_path.shape[0]):
                    region_left_gripper = np.linspace(left_gripper_path[gripper_step-1], left_gripper_path[gripper_step], left_gripper_step[gripper_step]+1)[1:]
                    left_gripper = left_gripper + region_left_gripper.tolist()
                left_gripper = np.array(left_gripper)

                right_gripper = []
                for gripper_step in range(1, right_gripper_path.shape[0]):
                    region_right_gripper = np.linspace(right_gripper_path[gripper_step-1], right_gripper_path[gripper_step], right_gripper_step[gripper_step]+1)[1:]
                    right_gripper = right_gripper + region_right_gripper.tolist()
                right_gripper = np.array(right_gripper)

                now_left_id = 0 if topp_left_flag else 1e9
                now_right_id = 0 if topp_right_flag else 1e9

                render_i = 0

                while now_left_id < left_n_step or now_right_id < right_n_step:
                    if topp_left_flag and now_left_id < left_n_step and now_left_id / left_n_step <= now_right_id / right_n_step:
                        self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id],'left')
                        if not self.fix_gripper:
                            self.robot.set_gripper(left_gripper[now_left_id], 'left')

                        now_left_id +=1

                    if topp_right_flag and now_right_id < right_n_step and now_right_id / right_n_step <= now_left_id / left_n_step:
                        self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id],'right')
                        if not self.fix_gripper:
                            self.robot.set_gripper(right_gripper[now_right_id], 'right')

                        now_right_id +=1

                    self.scene.step()
                    self._update_render()

                    if self.render_freq and render_i % self.render_freq == 0:
                        self._update_render()
                        if self.viewer: self.viewer.render()

                    render_i += 1

                    if self.check_success():
                        success_flag = True
                        break

                    if self.actor_pose == False:
                        break

                self._update_render()

                # update obs
                observation = self.get_obs()
                obs = dict()
                obs['point_cloud'] = observation['pointcloud']
                if self.dual_arm:
                    obs['agent_pos'] = observation['joint_action']
                    assert obs['agent_pos'].shape[0] == 14, 'agent_pose shape, error'
                else:
                    obs['agent_pos'] = observation['joint_action']
                    assert obs['agent_pos'].shape[0] == 7, 'agent_pose shape, error'
                model.update_obs(obs)

                if step_cnt % 10 == 0 and eval_video_log:
                    ffmpeg.stdin.write(observation['observation']['head_camera']['rgb'].tobytes())

                if self.render_freq:
                    if self.viewer: self.viewer.render()

                step_cnt += 1
                print(f'step: {step_cnt} / {self.step_lim}', end='\r')

                if success_flag:
                    print("\nsuccess!")
                    self.suc +=1

                    if eval_video_log:
                        ffmpeg.stdin.close()
                        ffmpeg.wait()
                        del ffmpeg

                    return

                if self.actor_pose == False:
                    break

                if step_cnt == self.step_lim:
                    break

        print("\nfail!")

        if eval_video_log:
            ffmpeg.stdin.close()
            ffmpeg.wait()
            del ffmpeg


    def apply_rdt(self, model, args):
        step_cnt = 0
        rdt_step = args['rdt_step']
        self.test_num += 1

        eval_video_log = args['eval_video_log']
        camera_config = get_camera_config(str(args['camera']['head_camera_type']))
        video_size = str(camera_config['w']) + 'x' + str(camera_config['h'])
        save_dir = 'RDT/' + str(args['task_name']) + '_' + str(args['model_name']) + '/' + str(args['checkpoint_id']) + '_seed' + str(args['eval_seed'])

        if eval_video_log:
            save_dir = Path('eval_video') / save_dir
            save_dir.mkdir(parents=True, exist_ok=True)
            ffmpeg = subprocess.Popen([
                'ffmpeg', '-y', '-loglevel', 'error',
                '-f', 'rawvideo',
                '-pixel_format', 'rgb24',
                '-video_size', video_size,
                '-framerate', '10',
                '-i', '-',
                '-pix_fmt', 'yuv420p',
                '-vcodec', 'libx264',
                '-crf', '23',
                f'{save_dir}/{self.test_num}.mp4'
            ], stdin=subprocess.PIPE)

        success_flag = False
        self._update_render()

        if self.render_freq:
            if self.viewer: self.viewer.render()

        self.actor_pose = True

        while step_cnt < self.step_lim:
            observation = self.get_obs()

            observation['observation']['head_camera']['rgb'] = observation['observation']['head_camera']['rgb'][:,:,::-1]
            observation['observation']['left_camera']['rgb'] = observation['observation']['left_camera']['rgb'][:,:,::-1]
            observation['observation']['right_camera']['rgb'] = observation['observation']['right_camera']['rgb'][:,:,::-1]
            obs = self.get_cam_obs(observation)
            obs['agent_pos'] = observation['joint_action']
            input_rgb_arr, input_state = [observation['observation']['head_camera']['rgb'], observation['observation']['right_camera']['rgb'], observation['observation']['left_camera']['rgb']], obs['agent_pos']
            if step_cnt == 0:
                model.update_observation_window(input_rgb_arr, input_state)
            pred_actions = model.get_action()
            take_actions = pred_actions[:rdt_step]

            for action in take_actions:
                actions = np.array([action])

                left_jointstate = self.robot.get_left_arm_jointState()
                right_jointstate = self.robot.get_right_arm_jointState()
                current_jointstate = np.array(left_jointstate + right_jointstate)
                left_arm_actions , left_gripper_actions , left_current_qpos, left_path = [], [], [], []
                right_arm_actions, right_gripper_actions, right_current_qpos, right_path = [], [], [], []

                left_arm_actions, left_gripper_actions = actions[:, :6],actions[:, 6]
                right_arm_actions, right_gripper_actions = actions[:, 7:13],actions[:, 13]

                left_current_qpos, right_current_qpos = current_jointstate[:6], current_jointstate[7:13]
                left_current_gripper, right_current_gripper = current_jointstate[6:7], current_jointstate[13:14]

                left_path = np.vstack((left_current_qpos, left_arm_actions))
                left_gripper_path = np.hstack((left_current_gripper, left_gripper_actions))

                right_path = np.vstack((right_current_qpos, right_arm_actions))
                right_gripper_path = np.hstack((right_current_gripper, right_gripper_actions))

                topp_left_flag, topp_right_flag = True, True

                try:
                    times, left_pos, left_vel, acc, duration = self.robot.left_planner.planner.TOPP(left_path, 1/250, verbose=True)
                    left_result = dict()
                    left_result['position'], left_result['velocity'] = left_pos, left_vel
                    left_n_step = left_result["position"].shape[0]
                except Exception as e:
                    print('left arm TOPP error: ', e)
                    topp_left_flag = False
                    left_n_step = 1

                if left_n_step == 0:
                    topp_left_flag = False
                    left_n_step = 1

                try:
                    times, right_pos, right_vel, acc, duration = self.robot.right_planner.planner.TOPP(right_path, 1/250, verbose=True)
                    right_result = dict()
                    right_result['position'], right_result['velocity'] = right_pos, right_vel
                    right_n_step = right_result["position"].shape[0]
                except Exception as e:
                    print('right arm TOPP error: ', e)
                    topp_right_flag = False
                    right_n_step = 1

                if right_n_step == 0:
                    topp_right_flag = False
                    right_n_step = 1

                n_step = max(left_n_step, right_n_step)

                obs_update_freq = n_step // actions.shape[0]

                left_mod_num = left_n_step % len(left_gripper_actions)
                right_mod_num = right_n_step % len(right_gripper_actions)
                left_gripper_step = [0] + [left_n_step // len(left_gripper_actions) + (1 if i < left_mod_num else 0) for i in range(len(left_gripper_actions))]
                right_gripper_step = [0] + [right_n_step // len(right_gripper_actions) + (1 if i < right_mod_num else 0) for i in range(len(right_gripper_actions))]

                left_gripper = []
                for gripper_step in range(1, left_gripper_path.shape[0]):
                    region_left_gripper = np.linspace(left_gripper_path[gripper_step-1], left_gripper_path[gripper_step], left_gripper_step[gripper_step]+1)[1:]
                    left_gripper = left_gripper + region_left_gripper.tolist()
                left_gripper = np.array(left_gripper)

                right_gripper = []
                for gripper_step in range(1, right_gripper_path.shape[0]):
                    region_right_gripper = np.linspace(right_gripper_path[gripper_step-1], right_gripper_path[gripper_step], right_gripper_step[gripper_step]+1)[1:]
                    right_gripper = right_gripper + region_right_gripper.tolist()
                right_gripper = np.array(right_gripper)

                now_left_id = 0 if topp_left_flag else 1e9
                now_right_id = 0 if topp_right_flag else 1e9

                render_i = 0

                while now_left_id < left_n_step or now_right_id < right_n_step:
                    if topp_left_flag and now_left_id < left_n_step and now_left_id / left_n_step <= now_right_id / right_n_step:
                        self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id],'left')
                        if not self.fix_gripper:
                            self.robot.set_gripper(left_gripper[now_left_id], 'left')

                        now_left_id +=1

                    if topp_right_flag and now_right_id < right_n_step and now_right_id / right_n_step <= now_left_id / left_n_step:
                        self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id],'right')
                        if not self.fix_gripper:
                            self.robot.set_gripper(right_gripper[now_right_id], 'right')

                        now_right_id +=1

                    self.scene.step()
                    self._update_render()

                    if self.render_freq and render_i % self.render_freq == 0:
                        self._update_render()
                        if self.viewer: self.viewer.render()

                    render_i += 1

                    if self.check_success():
                        success_flag = True
                        break

                    if self.actor_pose == False:
                        break

                self._update_render()

                # update obs
                observation = self.get_obs()
                observation['observation']['head_camera']['rgb'] = observation['observation']['head_camera']['rgb'][:,:,::-1]
                observation['observation']['left_camera']['rgb'] = observation['observation']['left_camera']['rgb'][:,:,::-1]
                observation['observation']['right_camera']['rgb'] = observation['observation']['right_camera']['rgb'][:,:,::-1]
                obs = self.get_cam_obs(observation)
                obs['agent_pos'] = observation['joint_action']

                input_rgb_arr, input_state = [observation['observation']['head_camera']['rgb'], observation['observation']['right_camera']['rgb'], observation['observation']['left_camera']['rgb']], obs['agent_pos'] # TODO
                model.update_observation_window(input_rgb_arr, input_state)

                if step_cnt % 10 == 0 and eval_video_log:
                    ffmpeg.stdin.write(observation['observation']['head_camera']['rgb'][:,:,::-1].tobytes())

                if self.render_freq:
                    if self.viewer: self.viewer.render()

                step_cnt += 1
                print(f'step: {step_cnt} / {self.step_lim}', end='\r')

                if success_flag:
                    print("\nsuccess!")
                    self.suc +=1

                    if eval_video_log:
                        ffmpeg.stdin.close()
                        ffmpeg.wait()
                        del ffmpeg

                    return

                if self.actor_pose == False:
                    break

                if step_cnt == self.step_lim:
                    break

        print("\nfail!")

        if eval_video_log:
            ffmpeg.stdin.close()
            ffmpeg.wait()
            del ffmpeg

    def take_action(self, action):
        if self.take_action_cnt == self.step_lim:
            return

        eval_video_freq = 1
        if self.eval_video_path is not None and self.take_action_cnt % eval_video_freq == 0:
            self.eval_video_ffmpeg.stdin.write(self.now_obs['observation']['head_camera']['rgb'].tobytes())

        self.take_action_cnt += 1
        print(f'step: {self.take_action_cnt} / {self.step_lim}', end='\r')

        # end_flag = False
        self._update_render()
        if self.render_freq:
            if self.viewer: self.viewer.render()

        # self.actor_pose = True
        actions = np.array([action])
        left_jointstate = self.robot.get_left_arm_jointState()
        right_jointstate = self.robot.get_right_arm_jointState()
        left_arm_dim = len(left_jointstate) - 1
        right_arm_dim = len(right_jointstate) - 1
        current_jointstate = np.array(left_jointstate + right_jointstate)

        left_arm_actions , left_gripper_actions , left_current_qpos, left_path = [], [], [], []
        right_arm_actions , right_gripper_actions , right_current_qpos, right_path = [], [], [], []
        if self.dual_arm:
            left_arm_actions,left_gripper_actions = actions[:, :left_arm_dim],actions[:, left_arm_dim]
            right_arm_actions,right_gripper_actions = actions[:, left_arm_dim+1:left_arm_dim+right_arm_dim+1],actions[:, left_arm_dim+right_arm_dim+1]
            left_current_qpos, right_current_qpos = current_jointstate[:left_arm_dim], current_jointstate[left_arm_dim+1:left_arm_dim+right_arm_dim+1]
            left_current_gripper, right_current_gripper = current_jointstate[left_arm_dim:left_arm_dim+1], current_jointstate[left_arm_dim+right_arm_dim+1:left_arm_dim+right_arm_dim+2]
        else:
            right_arm_actions,right_gripper_actions = actions[:, :6],actions[:, 6]
            right_current_qpos = current_jointstate[:6]
            right_current_gripper = current_jointstate[6:7]

        if self.dual_arm:
            left_path = np.vstack((left_current_qpos, left_arm_actions))
            left_gripper_path = np.hstack((left_current_gripper, left_gripper_actions))
        right_path = np.vstack((right_current_qpos, right_arm_actions))
        right_gripper_path = np.hstack((right_current_gripper, right_gripper_actions))

        topp_left_flag, topp_right_flag = True, True
        try:
            times, left_pos, left_vel, acc, duration = self.robot.left_mplib_planner.TOPP(left_path, 1/250, verbose=True)
            left_result = dict()
            left_result['position'], left_result['velocity'] = left_pos, left_vel
            left_n_step = left_result["position"].shape[0]
            # left_gripper = np.linspace(left_gripper[0], left_gripper[-1], left_n_step)
        except Exception as e:
            print('left arm TOPP error: ', e)
            topp_left_flag = False
            left_n_step = 1

        if left_n_step == 0 or (not self.dual_arm):
            topp_left_flag = False
            left_n_step = 1

        try:
            times, right_pos, right_vel, acc, duration = self.robot.right_mplib_planner.TOPP(right_path, 1/250, verbose=True)
            right_result = dict()
            right_result['position'], right_result['velocity'] = right_pos, right_vel
            right_n_step = right_result["position"].shape[0]
            # right_gripper = np.linspace(right_gripper[0], right_gripper[-1], right_n_step)
        except Exception as e:
            print('right arm TOPP error: ', e)
            topp_right_flag = False
            right_n_step = 1

        if right_n_step == 0:
            topp_right_flag = False
            right_n_step = 1

        n_step = max(left_n_step, right_n_step)

        obs_update_freq = n_step // actions.shape[0]

        # Calculate gripper step path
        left_mod_num = left_n_step % len(left_gripper_actions)
        right_mod_num = right_n_step % len(right_gripper_actions)
        left_gripper_step = [0] + [left_n_step // len(left_gripper_actions) + (1 if i < left_mod_num else 0) for i in range(len(left_gripper_actions))]
        right_gripper_step = [0] + [right_n_step // len(right_gripper_actions) + (1 if i < right_mod_num else 0) for i in range(len(right_gripper_actions))]

        left_gripper = []
        for gripper_step in range(1, left_gripper_path.shape[0]):
            region_left_gripper = np.linspace(left_gripper_path[gripper_step-1], left_gripper_path[gripper_step], left_gripper_step[gripper_step]+1)[1:]
            left_gripper = left_gripper + region_left_gripper.tolist()
        left_gripper = np.array(left_gripper)

        right_gripper = []
        for gripper_step in range(1, right_gripper_path.shape[0]):
            region_right_gripper = np.linspace(right_gripper_path[gripper_step-1], right_gripper_path[gripper_step], right_gripper_step[gripper_step]+1)[1:]
            right_gripper = right_gripper + region_right_gripper.tolist()
        right_gripper = np.array(right_gripper)

        now_left_id = 0 if topp_left_flag else 1e9
        now_right_id = 0 if topp_right_flag else 1e9

        i = 0

        while now_left_id < left_n_step or now_right_id < right_n_step:
            if topp_left_flag and now_left_id < left_n_step and now_left_id / left_n_step <= now_right_id / right_n_step:
                self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id],'left')
                if not self.fix_gripper:
                    self.robot.set_gripper(left_gripper[now_left_id], 'left')

                now_left_id +=1

            if topp_right_flag and now_right_id < right_n_step and now_right_id / right_n_step <= now_left_id / left_n_step:
                self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id],'right')
                if not self.fix_gripper:
                    self.robot.set_gripper(right_gripper[now_right_id], 'right')

                now_right_id +=1

            self.scene.step()
            self._update_render()

            i+=1
            # self.episode_score = max(self.episode_score, self.stage_reward())
            if self.check_success():
                self.eval_success = True
                return

        self. _update_render()

        if self.render_freq:
            if self.viewer: self.viewer.render()